using UnityEngine;
using System.Collections.Generic;

namespace CrystalQuest.Environment
{
    /// <summary>
    /// Spawns environmental objects like trees, rocks, and decorative elements
    /// </summary>
    public class EnvironmentSpawner : MonoBehaviour
    {
        [Header("Spawn Areas")]
        [SerializeField] private Transform[] spawnAreas;
        [SerializeField] private LayerMask groundLayer = 1;
        [SerializeField] private float spawnHeight = 100f;
        
        [Header("Trees")]
        [SerializeField] private GameObject[] treePrefabs;
        [SerializeField] private int treeCount = 50;
        [SerializeField] private float treeSpacing = 5f;
        [SerializeField] private Vector2 treeScaleRange = new Vector2(0.8f, 1.2f);
        
        [Header("Rocks")]
        [SerializeField] private GameObject[] rockPrefabs;
        [SerializeField] private int rockCount = 30;
        [SerializeField] private float rockSpacing = 3f;
        [SerializeField] private Vector2 rockScaleRange = new Vector2(0.5f, 1.5f);
        
        [Header("Grass")]
        [SerializeField] private GameObject[] grassPrefabs;
        [SerializeField] private int grassCount = 100;
        [SerializeField] private float grassSpacing = 2f;
        [SerializeField] private Vector2 grassScaleRange = new Vector2(0.8f, 1.2f);
        
        [Header("Crystals")]
        [SerializeField] private GameObject crystalPrefab;
        [SerializeField] private int crystalCount = 10;
        [SerializeField] private float crystalSpacing = 10f;
        [SerializeField] private float minCrystalDistance = 5f;
        
        [Header("Spawn Settings")]
        [SerializeField] private bool spawnOnStart = true;
        [SerializeField] private bool avoidSteepSlopes = true;
        [SerializeField] private float maxSlope = 30f;
        [SerializeField] private bool useRandomSeed = false;
        [SerializeField] private int randomSeed = 12345;
        
        // Private variables
        private List<Vector3> spawnedPositions = new List<Vector3>();
        private List<GameObject> spawnedObjects = new List<GameObject>();
        
        private void Start()
        {
            if (spawnOnStart)
            {
                SpawnAllObjects();
            }
        }
        
        [ContextMenu("Spawn All Objects")]
        public void SpawnAllObjects()
        {
            ClearSpawnedObjects();
            
            if (useRandomSeed)
            {
                Random.InitState(randomSeed);
            }
            
            SpawnTrees();
            SpawnRocks();
            SpawnGrass();
            SpawnCrystals();
            
            Debug.Log($"EnvironmentSpawner: Spawned {spawnedObjects.Count} objects");
        }
        
        [ContextMenu("Clear All Objects")]
        public void ClearSpawnedObjects()
        {
            foreach (GameObject obj in spawnedObjects)
            {
                if (obj != null)
                {
                    DestroyImmediate(obj);
                }
            }
            
            spawnedObjects.Clear();
            spawnedPositions.Clear();
        }
        
        private void SpawnTrees()
        {
            if (treePrefabs == null || treePrefabs.Length == 0) return;
            
            for (int i = 0; i < treeCount; i++)
            {
                Vector3 spawnPosition = GetRandomSpawnPosition();
                
                if (spawnPosition != Vector3.zero && IsValidSpawnPosition(spawnPosition, treeSpacing))
                {
                    GameObject treePrefab = treePrefabs[Random.Range(0, treePrefabs.Length)];
                    GameObject tree = SpawnObject(treePrefab, spawnPosition, "Trees");
                    
                    if (tree != null)
                    {
                        // Random scale
                        float scale = Random.Range(treeScaleRange.x, treeScaleRange.y);
                        tree.transform.localScale = Vector3.one * scale;
                        
                        // Random rotation
                        tree.transform.rotation = Quaternion.Euler(0, Random.Range(0f, 360f), 0);
                        
                        spawnedPositions.Add(spawnPosition);
                        spawnedObjects.Add(tree);
                    }
                }
            }
        }
        
        private void SpawnRocks()
        {
            if (rockPrefabs == null || rockPrefabs.Length == 0) return;
            
            for (int i = 0; i < rockCount; i++)
            {
                Vector3 spawnPosition = GetRandomSpawnPosition();
                
                if (spawnPosition != Vector3.zero && IsValidSpawnPosition(spawnPosition, rockSpacing))
                {
                    GameObject rockPrefab = rockPrefabs[Random.Range(0, rockPrefabs.Length)];
                    GameObject rock = SpawnObject(rockPrefab, spawnPosition, "Rocks");
                    
                    if (rock != null)
                    {
                        // Random scale
                        float scale = Random.Range(rockScaleRange.x, rockScaleRange.y);
                        rock.transform.localScale = Vector3.one * scale;
                        
                        // Random rotation
                        rock.transform.rotation = Quaternion.Euler(
                            Random.Range(-10f, 10f),
                            Random.Range(0f, 360f),
                            Random.Range(-10f, 10f)
                        );
                        
                        spawnedPositions.Add(spawnPosition);
                        spawnedObjects.Add(rock);
                    }
                }
            }
        }
        
        private void SpawnGrass()
        {
            if (grassPrefabs == null || grassPrefabs.Length == 0) return;
            
            for (int i = 0; i < grassCount; i++)
            {
                Vector3 spawnPosition = GetRandomSpawnPosition();
                
                if (spawnPosition != Vector3.zero && IsValidSpawnPosition(spawnPosition, grassSpacing))
                {
                    GameObject grassPrefab = grassPrefabs[Random.Range(0, grassPrefabs.Length)];
                    GameObject grass = SpawnObject(grassPrefab, spawnPosition, "Grass");
                    
                    if (grass != null)
                    {
                        // Random scale
                        float scale = Random.Range(grassScaleRange.x, grassScaleRange.y);
                        grass.transform.localScale = Vector3.one * scale;
                        
                        // Random rotation
                        grass.transform.rotation = Quaternion.Euler(0, Random.Range(0f, 360f), 0);
                        
                        spawnedPositions.Add(spawnPosition);
                        spawnedObjects.Add(grass);
                    }
                }
            }
        }
        
        private void SpawnCrystals()
        {
            if (crystalPrefab == null) return;
            
            for (int i = 0; i < crystalCount; i++)
            {
                Vector3 spawnPosition = GetRandomSpawnPosition();
                
                if (spawnPosition != Vector3.zero && IsValidSpawnPosition(spawnPosition, crystalSpacing))
                {
                    GameObject crystal = SpawnObject(crystalPrefab, spawnPosition, "Crystals");
                    
                    if (crystal != null)
                    {
                        // Ensure crystals have the Crystal component
                        if (crystal.GetComponent<CrystalQuest.Player.Crystal>() == null)
                        {
                            crystal.AddComponent<CrystalQuest.Player.Crystal>();
                        }
                        
                        // Make sure it has a trigger collider
                        Collider collider = crystal.GetComponent<Collider>();
                        if (collider != null)
                        {
                            collider.isTrigger = true;
                        }
                        
                        spawnedPositions.Add(spawnPosition);
                        spawnedObjects.Add(crystal);
                    }
                }
            }
        }
        
        private Vector3 GetRandomSpawnPosition()
        {
            Vector3 randomPosition = Vector3.zero;
            int attempts = 0;
            int maxAttempts = 50;
            
            while (attempts < maxAttempts)
            {
                // Choose random spawn area or use transform bounds
                Bounds spawnBounds = GetSpawnBounds();
                
                // Random position within bounds
                float x = Random.Range(spawnBounds.min.x, spawnBounds.max.x);
                float z = Random.Range(spawnBounds.min.z, spawnBounds.max.z);
                
                // Raycast down to find ground
                Vector3 rayStart = new Vector3(x, spawnHeight, z);
                RaycastHit hit;
                
                if (Physics.Raycast(rayStart, Vector3.down, out hit, spawnHeight * 2f, groundLayer))
                {
                    randomPosition = hit.point;
                    
                    // Check slope if required
                    if (!avoidSteepSlopes || Vector3.Angle(hit.normal, Vector3.up) <= maxSlope)
                    {
                        break;
                    }
                }
                
                attempts++;
            }
            
            return randomPosition;
        }
        
        private Bounds GetSpawnBounds()
        {
            if (spawnAreas != null && spawnAreas.Length > 0)
            {
                // Use random spawn area
                Transform spawnArea = spawnAreas[Random.Range(0, spawnAreas.Length)];
                
                // Get bounds from collider or use transform position with default size
                Collider areaCollider = spawnArea.GetComponent<Collider>();
                if (areaCollider != null)
                {
                    return areaCollider.bounds;
                }
                else
                {
                    return new Bounds(spawnArea.position, Vector3.one * 50f);
                }
            }
            else
            {
                // Use transform position with default bounds
                return new Bounds(transform.position, Vector3.one * 100f);
            }
        }
        
        private bool IsValidSpawnPosition(Vector3 position, float minDistance)
        {
            foreach (Vector3 existingPosition in spawnedPositions)
            {
                if (Vector3.Distance(position, existingPosition) < minDistance)
                {
                    return false;
                }
            }
            
            // Special check for crystals - ensure minimum distance from player spawn
            if (minDistance == crystalSpacing)
            {
                GameObject player = GameObject.FindGameObjectWithTag("Player");
                if (player != null)
                {
                    if (Vector3.Distance(position, player.transform.position) < minCrystalDistance)
                    {
                        return false;
                    }
                }
            }
            
            return true;
        }
        
        private GameObject SpawnObject(GameObject prefab, Vector3 position, string parentName)
        {
            // Create parent object if it doesn't exist
            Transform parent = transform.Find(parentName);
            if (parent == null)
            {
                GameObject parentGO = new GameObject(parentName);
                parentGO.transform.SetParent(transform);
                parent = parentGO.transform;
            }
            
            // Instantiate object
            GameObject spawnedObject = Instantiate(prefab, position, Quaternion.identity, parent);
            return spawnedObject;
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw spawn areas
            if (spawnAreas != null)
            {
                Gizmos.color = Color.green;
                foreach (Transform spawnArea in spawnAreas)
                {
                    if (spawnArea != null)
                    {
                        Collider areaCollider = spawnArea.GetComponent<Collider>();
                        if (areaCollider != null)
                        {
                            Gizmos.DrawWireCube(areaCollider.bounds.center, areaCollider.bounds.size);
                        }
                        else
                        {
                            Gizmos.DrawWireCube(spawnArea.position, Vector3.one * 50f);
                        }
                    }
                }
            }
            else
            {
                // Draw default spawn area
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireCube(transform.position, Vector3.one * 100f);
            }
            
            // Draw spawned positions
            if (Application.isPlaying && spawnedPositions != null)
            {
                Gizmos.color = Color.red;
                foreach (Vector3 position in spawnedPositions)
                {
                    Gizmos.DrawWireSphere(position, 1f);
                }
            }
        }
        
        private void OnValidate()
        {
            treeCount = Mathf.Max(0, treeCount);
            rockCount = Mathf.Max(0, rockCount);
            grassCount = Mathf.Max(0, grassCount);
            crystalCount = Mathf.Max(0, crystalCount);
            
            treeSpacing = Mathf.Max(1f, treeSpacing);
            rockSpacing = Mathf.Max(1f, rockSpacing);
            grassSpacing = Mathf.Max(0.5f, grassSpacing);
            crystalSpacing = Mathf.Max(5f, crystalSpacing);
            
            maxSlope = Mathf.Clamp(maxSlope, 0f, 90f);
            spawnHeight = Mathf.Max(10f, spawnHeight);
        }
    }
}
