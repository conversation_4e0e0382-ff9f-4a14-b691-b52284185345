using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Collections;

namespace CrystalQuest.UI
{
    /// <summary>
    /// Advanced menu system with smooth transitions and navigation
    /// </summary>
    public class MenuSystem : MonoBehaviour
    {
        [Header("Menu Panels")]
        [SerializeField] private List<MenuPanel> menuPanels = new List<MenuPanel>();
        [SerializeField] private MenuPanel currentPanel;
        [SerializeField] private MenuPanel defaultPanel;
        
        [Header("Transition Settings")]
        [SerializeField] private bool enableTransitions = true;
        [SerializeField] private float transitionDuration = 0.3f;
        [SerializeField] private AnimationCurve transitionCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private MenuTransitionType transitionType = MenuTransitionType.Fade;
        
        [Header("Navigation")]
        [SerializeField] private bool enableKeyboardNavigation = true;
        [SerializeField] private KeyCode backKey = KeyCode.Escape;
        [SerializeField] private bool enableMouseNavigation = true;
        
        [Header("Audio")]
        [SerializeField] private AudioSource audioSource;
        [SerializeField] private AudioClip menuOpenSound;
        [SerializeField] private AudioClip menuCloseSound;
        [SerializeField] private AudioClip buttonHoverSound;
        [SerializeField] private AudioClip buttonClickSound;
        
        // Private variables
        private Stack<MenuPanel> navigationStack = new Stack<MenuPanel>();
        private bool isTransitioning = false;
        private Dictionary<string, MenuPanel> panelLookup = new Dictionary<string, MenuPanel>();
        
        // Events
        public System.Action<MenuPanel> OnMenuOpened;
        public System.Action<MenuPanel> OnMenuClosed;
        public System.Action<MenuPanel, MenuPanel> OnMenuChanged;
        
        private void Awake()
        {
            InitializeMenuSystem();
        }
        
        private void InitializeMenuSystem()
        {
            // Set up audio source
            if (audioSource == null)
            {
                audioSource = GetComponent<AudioSource>();
                if (audioSource == null)
                {
                    audioSource = gameObject.AddComponent<AudioSource>();
                }
            }
            
            // Build panel lookup dictionary
            foreach (MenuPanel panel in menuPanels)
            {
                if (panel != null && !string.IsNullOrEmpty(panel.panelId))
                {
                    panelLookup[panel.panelId] = panel;
                    
                    // Set up panel
                    SetupMenuPanel(panel);
                }
            }
            
            // Hide all panels initially
            foreach (MenuPanel panel in menuPanels)
            {
                if (panel != null && panel.panelObject != null)
                {
                    panel.panelObject.SetActive(false);
                }
            }
            
            // Show default panel
            if (defaultPanel != null)
            {
                ShowPanel(defaultPanel.panelId, false);
            }
        }
        
        private void SetupMenuPanel(MenuPanel panel)
        {
            if (panel.panelObject == null) return;
            
            // Set up canvas group for transitions
            if (panel.canvasGroup == null)
            {
                panel.canvasGroup = panel.panelObject.GetComponent<CanvasGroup>();
                if (panel.canvasGroup == null)
                {
                    panel.canvasGroup = panel.panelObject.AddComponent<CanvasGroup>();
                }
            }
            
            // Set up buttons
            SetupPanelButtons(panel);
        }
        
        private void SetupPanelButtons(MenuPanel panel)
        {
            Button[] buttons = panel.panelObject.GetComponentsInChildren<Button>();
            
            foreach (Button button in buttons)
            {
                // Add hover sound
                var trigger = button.gameObject.GetComponent<UnityEngine.EventSystems.EventTrigger>();
                if (trigger == null)
                {
                    trigger = button.gameObject.AddComponent<UnityEngine.EventSystems.EventTrigger>();
                }
                
                // Add pointer enter event
                var pointerEnter = new UnityEngine.EventSystems.EventTrigger.Entry();
                pointerEnter.eventID = UnityEngine.EventSystems.EventTriggerType.PointerEnter;
                pointerEnter.callback.AddListener((data) => PlayButtonHoverSound());
                trigger.triggers.Add(pointerEnter);
                
                // Add click sound to existing onClick
                button.onClick.AddListener(PlayButtonClickSound);
            }
        }
        
        private void Update()
        {
            HandleInput();
        }
        
        private void HandleInput()
        {
            if (enableKeyboardNavigation && Input.GetKeyDown(backKey))
            {
                GoBack();
            }
        }
        
        public void ShowPanel(string panelId, bool addToStack = true)
        {
            if (isTransitioning) return;
            
            MenuPanel targetPanel = GetPanel(panelId);
            if (targetPanel == null)
            {
                Debug.LogWarning($"MenuSystem: Panel '{panelId}' not found");
                return;
            }
            
            if (currentPanel == targetPanel) return;
            
            MenuPanel previousPanel = currentPanel;
            
            if (addToStack && currentPanel != null)
            {
                navigationStack.Push(currentPanel);
            }
            
            if (enableTransitions)
            {
                StartCoroutine(TransitionToPanel(previousPanel, targetPanel));
            }
            else
            {
                SetPanelActive(previousPanel, false);
                SetPanelActive(targetPanel, true);
                currentPanel = targetPanel;
                OnPanelChanged(previousPanel, targetPanel);
            }
        }
        
        public void HideCurrentPanel()
        {
            if (currentPanel != null)
            {
                if (enableTransitions)
                {
                    StartCoroutine(HidePanelCoroutine(currentPanel));
                }
                else
                {
                    SetPanelActive(currentPanel, false);
                    OnMenuClosed?.Invoke(currentPanel);
                    currentPanel = null;
                }
            }
        }
        
        public void GoBack()
        {
            if (navigationStack.Count > 0)
            {
                MenuPanel previousPanel = navigationStack.Pop();
                ShowPanel(previousPanel.panelId, false);
            }
            else if (currentPanel != defaultPanel)
            {
                ShowPanel(defaultPanel.panelId, false);
            }
        }
        
        public void ClearNavigationStack()
        {
            navigationStack.Clear();
        }
        
        private IEnumerator TransitionToPanel(MenuPanel fromPanel, MenuPanel toPanel)
        {
            isTransitioning = true;
            
            // Prepare target panel
            if (toPanel.panelObject != null)
            {
                toPanel.panelObject.SetActive(true);
                if (toPanel.canvasGroup != null)
                {
                    toPanel.canvasGroup.alpha = 0f;
                    toPanel.canvasGroup.interactable = false;
                }
            }
            
            float elapsed = 0f;
            
            while (elapsed < transitionDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = transitionCurve.Evaluate(elapsed / transitionDuration);
                
                // Transition out current panel
                if (fromPanel != null && fromPanel.canvasGroup != null)
                {
                    ApplyTransition(fromPanel, 1f - progress, false);
                }
                
                // Transition in target panel
                if (toPanel.canvasGroup != null)
                {
                    ApplyTransition(toPanel, progress, true);
                }
                
                yield return null;
            }
            
            // Finalize transition
            if (fromPanel != null)
            {
                SetPanelActive(fromPanel, false);
            }
            
            if (toPanel.canvasGroup != null)
            {
                toPanel.canvasGroup.alpha = 1f;
                toPanel.canvasGroup.interactable = true;
            }
            
            currentPanel = toPanel;
            isTransitioning = false;
            
            OnPanelChanged(fromPanel, toPanel);
        }
        
        private IEnumerator HidePanelCoroutine(MenuPanel panel)
        {
            isTransitioning = true;
            
            float elapsed = 0f;
            
            while (elapsed < transitionDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = transitionCurve.Evaluate(elapsed / transitionDuration);
                
                ApplyTransition(panel, 1f - progress, false);
                
                yield return null;
            }
            
            SetPanelActive(panel, false);
            currentPanel = null;
            isTransitioning = false;
            
            OnMenuClosed?.Invoke(panel);
        }
        
        private void ApplyTransition(MenuPanel panel, float progress, bool isEntering)
        {
            if (panel.canvasGroup == null) return;
            
            switch (transitionType)
            {
                case MenuTransitionType.Fade:
                    panel.canvasGroup.alpha = progress;
                    break;
                    
                case MenuTransitionType.Scale:
                    panel.canvasGroup.alpha = progress;
                    float scale = isEntering ? Mathf.Lerp(0.8f, 1f, progress) : Mathf.Lerp(1f, 0.8f, 1f - progress);
                    panel.panelObject.transform.localScale = Vector3.one * scale;
                    break;
                    
                case MenuTransitionType.Slide:
                    panel.canvasGroup.alpha = progress;
                    RectTransform rectTransform = panel.panelObject.GetComponent<RectTransform>();
                    if (rectTransform != null)
                    {
                        float slideOffset = isEntering ? Mathf.Lerp(100f, 0f, progress) : Mathf.Lerp(0f, -100f, 1f - progress);
                        rectTransform.anchoredPosition = new Vector2(slideOffset, rectTransform.anchoredPosition.y);
                    }
                    break;
            }
            
            panel.canvasGroup.interactable = progress > 0.5f;
        }
        
        private void SetPanelActive(MenuPanel panel, bool active)
        {
            if (panel?.panelObject != null)
            {
                panel.panelObject.SetActive(active);
            }
        }
        
        private void OnPanelChanged(MenuPanel fromPanel, MenuPanel toPanel)
        {
            if (fromPanel != null)
            {
                OnMenuClosed?.Invoke(fromPanel);
            }
            
            if (toPanel != null)
            {
                OnMenuOpened?.Invoke(toPanel);
                PlayMenuOpenSound();
            }
            
            OnMenuChanged?.Invoke(fromPanel, toPanel);
        }
        
        private MenuPanel GetPanel(string panelId)
        {
            return panelLookup.ContainsKey(panelId) ? panelLookup[panelId] : null;
        }
        
        // Audio methods
        private void PlayMenuOpenSound()
        {
            if (audioSource != null && menuOpenSound != null)
            {
                audioSource.PlayOneShot(menuOpenSound);
            }
        }
        
        private void PlayMenuCloseSound()
        {
            if (audioSource != null && menuCloseSound != null)
            {
                audioSource.PlayOneShot(menuCloseSound);
            }
        }
        
        private void PlayButtonHoverSound()
        {
            if (audioSource != null && buttonHoverSound != null)
            {
                audioSource.PlayOneShot(buttonHoverSound, 0.5f);
            }
        }
        
        private void PlayButtonClickSound()
        {
            if (audioSource != null && buttonClickSound != null)
            {
                audioSource.PlayOneShot(buttonClickSound);
            }
        }
        
        // Public utility methods
        public bool IsCurrentPanel(string panelId)
        {
            return currentPanel != null && currentPanel.panelId == panelId;
        }
        
        public string GetCurrentPanelId()
        {
            return currentPanel?.panelId ?? "";
        }
        
        public bool CanGoBack()
        {
            return navigationStack.Count > 0 || (currentPanel != defaultPanel && defaultPanel != null);
        }
        
        public void SetDefaultPanel(string panelId)
        {
            MenuPanel panel = GetPanel(panelId);
            if (panel != null)
            {
                defaultPanel = panel;
            }
        }
        
        public void RegisterPanel(MenuPanel panel)
        {
            if (panel != null && !string.IsNullOrEmpty(panel.panelId))
            {
                if (!menuPanels.Contains(panel))
                {
                    menuPanels.Add(panel);
                }
                
                panelLookup[panel.panelId] = panel;
                SetupMenuPanel(panel);
            }
        }
        
        public void UnregisterPanel(string panelId)
        {
            if (panelLookup.ContainsKey(panelId))
            {
                MenuPanel panel = panelLookup[panelId];
                menuPanels.Remove(panel);
                panelLookup.Remove(panelId);
            }
        }
    }
    
    [System.Serializable]
    public class MenuPanel
    {
        [Header("Panel Settings")]
        public string panelId;
        public string displayName;
        public GameObject panelObject;
        public CanvasGroup canvasGroup;
        
        [Header("Navigation")]
        public bool allowBackNavigation = true;
        public string parentPanelId;
        
        [Header("Behavior")]
        public bool pauseGameWhenActive = false;
        public bool hideOtherPanels = true;
    }
    
    public enum MenuTransitionType
    {
        Fade,
        Scale,
        Slide
    }
}
