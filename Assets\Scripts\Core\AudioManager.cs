using UnityEngine;
using System.Collections.Generic;
using System.Collections;

namespace CrystalQuest.Core
{
    /// <summary>
    /// Manages all audio in the game including music, sound effects, and audio settings
    /// </summary>
    public class AudioManager : MonoBehaviour
    {
        [Header("Audio Sources")]
        [SerializeField] private AudioSource musicSource;
        [SerializeField] private AudioSource sfxSource;
        [SerializeField] private AudioSource ambientSource;
        
        [Header("Audio Clips")]
        [SerializeField] private AudioClip mainMenuMusic;
        [SerializeField] private AudioClip gameplayMusic;
        [SerializeField] private AudioClip victoryMusic;
        [SerializeField] private AudioClip gameOverMusic;
        
        [Header("Sound Effects")]
        [SerializeField] private AudioClip crystalCollectSFX;
        [SerializeField] private AudioClip jumpSFX;
        [SerializeField] private AudioClip footstepSFX;
        [SerializeField] private AudioClip buttonClickSFX;
        [SerializeField] private AudioClip gameOverSFX;
        [SerializeField] private AudioClip victorySFX;
        
        [Header("Audio Settings")]
        [SerializeField] private float masterVolume = 1f;
        [SerializeField] private float musicVolume = 0.7f;
        [SerializeField] private float sfxVolume = 0.8f;
        [SerializeField] private float ambientVolume = 0.5f;
        
        // Singleton instance
        public static AudioManager Instance { get; private set; }
        
        // Audio source pool for multiple simultaneous sound effects
        private Queue<AudioSource> sfxSourcePool = new Queue<AudioSource>();
        private const int SFX_POOL_SIZE = 10;
        
        // Properties
        public float MasterVolume 
        { 
            get => masterVolume; 
            set 
            { 
                masterVolume = Mathf.Clamp01(value);
                UpdateAllVolumes();
            } 
        }
        
        public float MusicVolume 
        { 
            get => musicVolume; 
            set 
            { 
                musicVolume = Mathf.Clamp01(value);
                UpdateMusicVolume();
            } 
        }
        
        public float SFXVolume 
        { 
            get => sfxVolume; 
            set 
            { 
                sfxVolume = Mathf.Clamp01(value);
                UpdateSFXVolume();
            } 
        }
        
        public float AmbientVolume 
        { 
            get => ambientVolume; 
            set 
            { 
                ambientVolume = Mathf.Clamp01(value);
                UpdateAmbientVolume();
            } 
        }
        
        private void Awake()
        {
            // Implement singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeAudioManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeAudioManager()
        {
            // Create audio sources if they don't exist
            if (musicSource == null)
            {
                GameObject musicGO = new GameObject("MusicSource");
                musicGO.transform.SetParent(transform);
                musicSource = musicGO.AddComponent<AudioSource>();
                musicSource.loop = true;
                musicSource.playOnAwake = false;
            }
            
            if (sfxSource == null)
            {
                GameObject sfxGO = new GameObject("SFXSource");
                sfxGO.transform.SetParent(transform);
                sfxSource = sfxGO.AddComponent<AudioSource>();
                sfxSource.playOnAwake = false;
            }
            
            if (ambientSource == null)
            {
                GameObject ambientGO = new GameObject("AmbientSource");
                ambientGO.transform.SetParent(transform);
                ambientSource = ambientGO.AddComponent<AudioSource>();
                ambientSource.loop = true;
                ambientSource.playOnAwake = false;
            }
            
            // Initialize SFX source pool
            InitializeSFXPool();
            
            // Set initial volumes
            UpdateAllVolumes();
            
            // Subscribe to game state changes
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStateChanged += OnGameStateChanged;
            }
            
            Debug.Log("AudioManager: Initialized successfully");
        }
        
        private void InitializeSFXPool()
        {
            for (int i = 0; i < SFX_POOL_SIZE; i++)
            {
                GameObject sfxGO = new GameObject($"SFXSource_{i}");
                sfxGO.transform.SetParent(transform);
                AudioSource source = sfxGO.AddComponent<AudioSource>();
                source.playOnAwake = false;
                sfxSourcePool.Enqueue(source);
            }
        }
        
        private void OnGameStateChanged(GameState newState)
        {
            switch (newState)
            {
                case GameState.MainMenu:
                    PlayMusic(mainMenuMusic);
                    break;
                case GameState.Playing:
                    PlayMusic(gameplayMusic);
                    break;
                case GameState.Victory:
                    PlayMusic(victoryMusic);
                    PlaySFX(victorySFX);
                    break;
                case GameState.GameOver:
                    PlayMusic(gameOverMusic);
                    PlaySFX(gameOverSFX);
                    break;
            }
        }
        
        public void PlayMusic(AudioClip clip, bool fadeIn = true)
        {
            if (clip == null) return;
            
            if (fadeIn && musicSource.isPlaying)
            {
                StartCoroutine(FadeOutAndPlayMusic(clip));
            }
            else
            {
                musicSource.clip = clip;
                musicSource.Play();
            }
        }
        
        private IEnumerator FadeOutAndPlayMusic(AudioClip newClip)
        {
            float startVolume = musicSource.volume;
            
            // Fade out current music
            while (musicSource.volume > 0)
            {
                musicSource.volume -= startVolume * Time.unscaledDeltaTime / 0.5f;
                yield return null;
            }
            
            // Change clip and fade in
            musicSource.clip = newClip;
            musicSource.Play();
            
            while (musicSource.volume < startVolume)
            {
                musicSource.volume += startVolume * Time.unscaledDeltaTime / 0.5f;
                yield return null;
            }
            
            musicSource.volume = startVolume;
        }
        
        public void PlaySFX(AudioClip clip, float volumeScale = 1f)
        {
            if (clip == null) return;
            
            AudioSource source = GetAvailableSFXSource();
            if (source != null)
            {
                source.clip = clip;
                source.volume = sfxVolume * masterVolume * volumeScale;
                source.Play();
            }
        }
        
        private AudioSource GetAvailableSFXSource()
        {
            // Find an available source from the pool
            foreach (AudioSource source in sfxSourcePool)
            {
                if (!source.isPlaying)
                {
                    return source;
                }
            }
            
            // If no available source, use the main SFX source
            return sfxSource;
        }
        
        public void PlaySFX3D(AudioClip clip, Vector3 position, float volumeScale = 1f)
        {
            if (clip == null) return;
            
            AudioSource.PlayClipAtPoint(clip, position, sfxVolume * masterVolume * volumeScale);
        }
        
        public void StopMusic()
        {
            musicSource.Stop();
        }
        
        public void PauseMusic()
        {
            musicSource.Pause();
        }
        
        public void ResumeMusic()
        {
            musicSource.UnPause();
        }
        
        private void UpdateAllVolumes()
        {
            UpdateMusicVolume();
            UpdateSFXVolume();
            UpdateAmbientVolume();
        }
        
        private void UpdateMusicVolume()
        {
            if (musicSource != null)
            {
                musicSource.volume = musicVolume * masterVolume;
            }
        }
        
        private void UpdateSFXVolume()
        {
            if (sfxSource != null)
            {
                sfxSource.volume = sfxVolume * masterVolume;
            }
            
            // Update pool sources
            foreach (AudioSource source in sfxSourcePool)
            {
                if (!source.isPlaying)
                {
                    source.volume = sfxVolume * masterVolume;
                }
            }
        }
        
        private void UpdateAmbientVolume()
        {
            if (ambientSource != null)
            {
                ambientSource.volume = ambientVolume * masterVolume;
            }
        }
        
        // Convenience methods for common sound effects
        public void PlayCrystalCollectSound() => PlaySFX(crystalCollectSFX);
        public void PlayJumpSound() => PlaySFX(jumpSFX);
        public void PlayFootstepSound() => PlaySFX(footstepSFX, 0.6f);
        public void PlayButtonClickSound() => PlaySFX(buttonClickSFX);
        
        private void OnDestroy()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStateChanged -= OnGameStateChanged;
            }
        }
    }
}
