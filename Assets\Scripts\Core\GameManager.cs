using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;

namespace CrystalQuest.Core
{
    /// <summary>
    /// Central game manager that handles game state, scene transitions, and core game loop
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        [Header("Game Settings")]
        [SerializeField] private int targetFrameRate = 60;
        [SerializeField] private bool enableVSync = true;
        
        [Header("Game State")]
        [SerializeField] private GameState currentState = GameState.MainMenu;
        [SerializeField] private int currentLevel = 1;
        [SerializeField] private int playerScore = 0;
        [SerializeField] private int crystalsCollected = 0;
        [SerializeField] private int totalCrystals = 0;
        
        // Singleton instance
        public static GameManager Instance { get; private set; }
        
        // Events for game state changes
        public System.Action<GameState> OnGameStateChanged;
        public System.Action<int> OnScoreChanged;
        public System.Action<int, int> OnCrystalsChanged; // collected, total
        
        // Properties
        public GameState CurrentState => currentState;
        public int CurrentLevel => currentLevel;
        public int PlayerScore => playerScore;
        public int CrystalsCollected => crystalsCollected;
        public int TotalCrystals => totalCrystals;
        public bool IsGamePaused => currentState == GameState.Paused;
        
        private void Awake()
        {
            // Implement singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeGame()
        {
            // Set application settings
            Application.targetFrameRate = targetFrameRate;
            QualitySettings.vSyncCount = enableVSync ? 1 : 0;
            
            // Initialize game systems
            Debug.Log("GameManager: Game initialized successfully");
        }
        
        private void Start()
        {
            // Start with main menu state
            ChangeGameState(GameState.MainMenu);
        }
        
        private void Update()
        {
            HandleInput();
        }
        
        private void HandleInput()
        {
            // Handle global input (pause, quit, etc.)
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                if (currentState == GameState.Playing)
                {
                    PauseGame();
                }
                else if (currentState == GameState.Paused)
                {
                    ResumeGame();
                }
            }
        }
        
        public void ChangeGameState(GameState newState)
        {
            if (currentState == newState) return;
            
            GameState previousState = currentState;
            currentState = newState;
            
            Debug.Log($"GameManager: State changed from {previousState} to {newState}");
            
            // Handle state-specific logic
            switch (newState)
            {
                case GameState.MainMenu:
                    Time.timeScale = 1f;
                    Cursor.lockState = CursorLockMode.None;
                    Cursor.visible = true;
                    break;
                    
                case GameState.Playing:
                    Time.timeScale = 1f;
                    Cursor.lockState = CursorLockMode.Locked;
                    Cursor.visible = false;
                    break;
                    
                case GameState.Paused:
                    Time.timeScale = 0f;
                    Cursor.lockState = CursorLockMode.None;
                    Cursor.visible = true;
                    break;
                    
                case GameState.GameOver:
                    Time.timeScale = 0f;
                    Cursor.lockState = CursorLockMode.None;
                    Cursor.visible = true;
                    break;
            }
            
            OnGameStateChanged?.Invoke(newState);
        }
        
        public void StartNewGame()
        {
            currentLevel = 1;
            playerScore = 0;
            crystalsCollected = 0;
            LoadLevel(currentLevel);
        }
        
        public void LoadLevel(int levelNumber)
        {
            currentLevel = levelNumber;
            string sceneName = $"Level{levelNumber:D2}";
            
            Debug.Log($"GameManager: Loading level {levelNumber}");
            StartCoroutine(LoadSceneAsync(sceneName));
        }
        
        private IEnumerator LoadSceneAsync(string sceneName)
        {
            ChangeGameState(GameState.Loading);
            
            AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(sceneName);
            
            while (!asyncLoad.isDone)
            {
                // You can update loading progress here
                yield return null;
            }
            
            ChangeGameState(GameState.Playing);
        }
        
        public void PauseGame()
        {
            if (currentState == GameState.Playing)
            {
                ChangeGameState(GameState.Paused);
            }
        }
        
        public void ResumeGame()
        {
            if (currentState == GameState.Paused)
            {
                ChangeGameState(GameState.Playing);
            }
        }
        
        public void AddScore(int points)
        {
            playerScore += points;
            OnScoreChanged?.Invoke(playerScore);
            Debug.Log($"GameManager: Score updated to {playerScore}");
        }
        
        public void CollectCrystal()
        {
            crystalsCollected++;
            AddScore(100); // Award points for crystal collection
            OnCrystalsChanged?.Invoke(crystalsCollected, totalCrystals);
            
            Debug.Log($"GameManager: Crystal collected! {crystalsCollected}/{totalCrystals}");
            
            // Check if level is complete
            if (crystalsCollected >= totalCrystals)
            {
                CompleteLevel();
            }
        }
        
        public void SetTotalCrystals(int total)
        {
            totalCrystals = total;
            OnCrystalsChanged?.Invoke(crystalsCollected, totalCrystals);
        }
        
        private void CompleteLevel()
        {
            Debug.Log("GameManager: Level completed!");
            AddScore(500); // Bonus for completing level
            
            // Load next level or show victory screen
            if (currentLevel < 3) // Assuming 3 levels for now
            {
                StartCoroutine(LoadNextLevelAfterDelay(2f));
            }
            else
            {
                ChangeGameState(GameState.Victory);
            }
        }
        
        private IEnumerator LoadNextLevelAfterDelay(float delay)
        {
            yield return new WaitForSeconds(delay);
            LoadLevel(currentLevel + 1);
        }
        
        public void GameOver()
        {
            Debug.Log("GameManager: Game Over");
            ChangeGameState(GameState.GameOver);
        }
        
        public void ReturnToMainMenu()
        {
            Time.timeScale = 1f;
            SceneManager.LoadScene("MainMenu");
            ChangeGameState(GameState.MainMenu);
        }
        
        public void QuitGame()
        {
            Debug.Log("GameManager: Quitting game");
            
            #if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }
    }
    
    public enum GameState
    {
        MainMenu,
        Loading,
        Playing,
        Paused,
        GameOver,
        Victory
    }
}
