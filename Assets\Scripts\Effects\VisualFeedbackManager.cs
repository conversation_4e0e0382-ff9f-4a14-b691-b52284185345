using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;
using System.Collections.Generic;

namespace CrystalQuest.Effects
{
    /// <summary>
    /// Manages visual feedback like floating text, screen effects, and UI animations
    /// </summary>
    public class VisualFeedbackManager : MonoBehaviour
    {
        [Header("Floating Text")]
        [SerializeField] private GameObject floatingTextPrefab;
        [SerializeField] private Transform floatingTextParent;
        [SerializeField] private int floatingTextPoolSize = 20;
        
        [Header("Screen Effects")]
        [SerializeField] private Image screenFlashImage;
        [SerializeField] private Image screenFadeImage;
        [SerializeField] private Image damageOverlay;
        [SerializeField] private Image healOverlay;
        
        [Header("UI Animations")]
        [SerializeField] private float defaultAnimationDuration = 0.5f;
        [SerializeField] private AnimationCurve defaultEaseCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        [Header("Colors")]
        [SerializeField] private Color scoreColor = Color.yellow;
        [SerializeField] private Color damageColor = Color.red;
        [SerializeField] private Color healColor = Color.green;
        [SerializeField] private Color crystalColor = Color.cyan;
        [SerializeField] private Color comboColor = Color.orange;
        
        // Singleton instance
        public static VisualFeedbackManager Instance { get; private set; }
        
        // Floating text pool
        private Queue<GameObject> floatingTextPool = new Queue<GameObject>();
        private List<FloatingText> activeFloatingTexts = new List<FloatingText>();
        
        // Screen effect coroutines
        private Coroutine screenFlashCoroutine;
        private Coroutine screenFadeCoroutine;
        private Coroutine damageOverlayCoroutine;
        private Coroutine healOverlayCoroutine;
        
        private void Awake()
        {
            // Implement singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeVisualFeedback();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeVisualFeedback()
        {
            // Set up floating text parent
            if (floatingTextParent == null)
            {
                GameObject parentGO = new GameObject("FloatingTextParent");
                parentGO.transform.SetParent(transform);
                floatingTextParent = parentGO.transform;
            }
            
            // Initialize floating text pool
            InitializeFloatingTextPool();
            
            // Set up screen effect images
            SetupScreenEffectImages();
            
            Debug.Log("VisualFeedbackManager: Initialized successfully");
        }
        
        private void InitializeFloatingTextPool()
        {
            if (floatingTextPrefab == null)
            {
                CreateDefaultFloatingTextPrefab();
            }
            
            for (int i = 0; i < floatingTextPoolSize; i++)
            {
                GameObject textObj = Instantiate(floatingTextPrefab, floatingTextParent);
                textObj.SetActive(false);
                floatingTextPool.Enqueue(textObj);
            }
        }
        
        private void CreateDefaultFloatingTextPrefab()
        {
            // Create a simple floating text prefab if none is assigned
            GameObject textGO = new GameObject("FloatingText");
            
            // Add Canvas component for world space UI
            Canvas canvas = textGO.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.WorldSpace;
            canvas.worldCamera = Camera.main;
            
            // Add CanvasScaler
            CanvasScaler scaler = textGO.AddComponent<CanvasScaler>();
            scaler.dynamicPixelsPerUnit = 10f;
            
            // Add text component
            GameObject textChild = new GameObject("Text");
            textChild.transform.SetParent(textGO.transform);
            
            TextMeshProUGUI textMesh = textChild.AddComponent<TextMeshProUGUI>();
            textMesh.text = "Sample Text";
            textMesh.fontSize = 24;
            textMesh.alignment = TextAlignmentOptions.Center;
            textMesh.color = Color.white;
            
            // Set rect transform
            RectTransform rectTransform = textChild.GetComponent<RectTransform>();
            rectTransform.sizeDelta = new Vector2(200, 50);
            rectTransform.anchoredPosition = Vector2.zero;
            
            floatingTextPrefab = textGO;
        }
        
        private void SetupScreenEffectImages()
        {
            // Create screen effect images if they don't exist
            Canvas screenCanvas = FindObjectOfType<Canvas>();
            if (screenCanvas == null) return;
            
            if (screenFlashImage == null)
            {
                screenFlashImage = CreateScreenEffectImage("ScreenFlash", screenCanvas.transform);
            }
            
            if (screenFadeImage == null)
            {
                screenFadeImage = CreateScreenEffectImage("ScreenFade", screenCanvas.transform);
            }
            
            if (damageOverlay == null)
            {
                damageOverlay = CreateScreenEffectImage("DamageOverlay", screenCanvas.transform);
                damageOverlay.color = new Color(1, 0, 0, 0);
            }
            
            if (healOverlay == null)
            {
                healOverlay = CreateScreenEffectImage("HealOverlay", screenCanvas.transform);
                healOverlay.color = new Color(0, 1, 0, 0);
            }
        }
        
        private Image CreateScreenEffectImage(string name, Transform parent)
        {
            GameObject imageGO = new GameObject(name);
            imageGO.transform.SetParent(parent);
            
            Image image = imageGO.AddComponent<Image>();
            image.color = new Color(0, 0, 0, 0);
            
            RectTransform rectTransform = imageGO.GetComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.sizeDelta = Vector2.zero;
            rectTransform.anchoredPosition = Vector2.zero;
            
            return image;
        }
        
        private void Update()
        {
            UpdateFloatingTexts();
        }
        
        private void UpdateFloatingTexts()
        {
            for (int i = activeFloatingTexts.Count - 1; i >= 0; i--)
            {
                FloatingText floatingText = activeFloatingTexts[i];
                
                if (floatingText.IsExpired)
                {
                    ReturnFloatingTextToPool(floatingText);
                    activeFloatingTexts.RemoveAt(i);
                }
                else
                {
                    floatingText.Update();
                }
            }
        }
        
        public void ShowFloatingText(string text, Vector3 worldPosition, Color color, float duration = 2f, float moveSpeed = 2f)
        {
            GameObject textObj = GetFloatingTextFromPool();
            if (textObj == null) return;
            
            // Set position
            textObj.transform.position = worldPosition;
            textObj.SetActive(true);
            
            // Configure text
            TextMeshProUGUI textMesh = textObj.GetComponentInChildren<TextMeshProUGUI>();
            if (textMesh != null)
            {
                textMesh.text = text;
                textMesh.color = color;
            }
            
            // Create floating text data
            FloatingText floatingText = new FloatingText
            {
                gameObject = textObj,
                startTime = Time.time,
                duration = duration,
                startPosition = worldPosition,
                moveSpeed = moveSpeed,
                textMesh = textMesh
            };
            
            activeFloatingTexts.Add(floatingText);
        }
        
        private GameObject GetFloatingTextFromPool()
        {
            if (floatingTextPool.Count > 0)
            {
                return floatingTextPool.Dequeue();
            }
            
            // Create new one if pool is empty
            if (floatingTextPrefab != null)
            {
                return Instantiate(floatingTextPrefab, floatingTextParent);
            }
            
            return null;
        }
        
        private void ReturnFloatingTextToPool(FloatingText floatingText)
        {
            if (floatingText.gameObject != null)
            {
                floatingText.gameObject.SetActive(false);
                floatingTextPool.Enqueue(floatingText.gameObject);
            }
        }
        
        // Convenience methods for common floating text
        public void ShowScoreText(int score, Vector3 position)
        {
            ShowFloatingText($"+{score}", position, scoreColor);
        }
        
        public void ShowDamageText(int damage, Vector3 position)
        {
            ShowFloatingText($"-{damage}", position, damageColor);
        }
        
        public void ShowHealText(int heal, Vector3 position)
        {
            ShowFloatingText($"+{heal}", position, healColor);
        }
        
        public void ShowCrystalText(Vector3 position)
        {
            ShowFloatingText("Crystal!", position, crystalColor);
        }
        
        public void ShowComboText(int combo, Vector3 position)
        {
            ShowFloatingText($"Combo x{combo}!", position, comboColor);
        }
        
        // Screen effects
        public void FlashScreen(Color color, float duration = 0.2f)
        {
            if (screenFlashImage == null) return;
            
            if (screenFlashCoroutine != null)
            {
                StopCoroutine(screenFlashCoroutine);
            }
            
            screenFlashCoroutine = StartCoroutine(ScreenFlashCoroutine(color, duration));
        }
        
        private IEnumerator ScreenFlashCoroutine(Color color, float duration)
        {
            screenFlashImage.color = color;
            
            float elapsed = 0f;
            while (elapsed < duration)
            {
                elapsed += Time.unscaledDeltaTime;
                float alpha = Mathf.Lerp(color.a, 0f, elapsed / duration);
                screenFlashImage.color = new Color(color.r, color.g, color.b, alpha);
                yield return null;
            }
            
            screenFlashImage.color = new Color(color.r, color.g, color.b, 0f);
        }
        
        public void FadeScreen(Color color, float duration = 1f, bool fadeIn = false)
        {
            if (screenFadeImage == null) return;
            
            if (screenFadeCoroutine != null)
            {
                StopCoroutine(screenFadeCoroutine);
            }
            
            screenFadeCoroutine = StartCoroutine(ScreenFadeCoroutine(color, duration, fadeIn));
        }
        
        private IEnumerator ScreenFadeCoroutine(Color color, float duration, bool fadeIn)
        {
            Color startColor = fadeIn ? color : new Color(color.r, color.g, color.b, 0f);
            Color endColor = fadeIn ? new Color(color.r, color.g, color.b, 0f) : color;
            
            float elapsed = 0f;
            while (elapsed < duration)
            {
                elapsed += Time.unscaledDeltaTime;
                screenFadeImage.color = Color.Lerp(startColor, endColor, elapsed / duration);
                yield return null;
            }
            
            screenFadeImage.color = endColor;
        }
        
        public void ShowDamageOverlay(float intensity = 0.3f, float duration = 0.5f)
        {
            if (damageOverlay == null) return;
            
            if (damageOverlayCoroutine != null)
            {
                StopCoroutine(damageOverlayCoroutine);
            }
            
            damageOverlayCoroutine = StartCoroutine(OverlayEffectCoroutine(damageOverlay, intensity, duration));
        }
        
        public void ShowHealOverlay(float intensity = 0.2f, float duration = 0.5f)
        {
            if (healOverlay == null) return;
            
            if (healOverlayCoroutine != null)
            {
                StopCoroutine(healOverlayCoroutine);
            }
            
            healOverlayCoroutine = StartCoroutine(OverlayEffectCoroutine(healOverlay, intensity, duration));
        }
        
        private IEnumerator OverlayEffectCoroutine(Image overlay, float intensity, float duration)
        {
            Color originalColor = overlay.color;
            Color targetColor = new Color(originalColor.r, originalColor.g, originalColor.b, intensity);
            
            // Fade in
            float elapsed = 0f;
            float fadeInDuration = duration * 0.3f;
            
            while (elapsed < fadeInDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                overlay.color = Color.Lerp(originalColor, targetColor, elapsed / fadeInDuration);
                yield return null;
            }
            
            // Hold
            float holdDuration = duration * 0.2f;
            yield return new WaitForSecondsRealtime(holdDuration);
            
            // Fade out
            elapsed = 0f;
            float fadeOutDuration = duration * 0.5f;
            
            while (elapsed < fadeOutDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                overlay.color = Color.Lerp(targetColor, originalColor, elapsed / fadeOutDuration);
                yield return null;
            }
            
            overlay.color = originalColor;
        }
        
        // UI Animation helpers
        public void AnimateScale(Transform target, Vector3 targetScale, float duration = -1f, AnimationCurve curve = null)
        {
            if (duration < 0) duration = defaultAnimationDuration;
            if (curve == null) curve = defaultEaseCurve;
            
            StartCoroutine(AnimateScaleCoroutine(target, targetScale, duration, curve));
        }
        
        private IEnumerator AnimateScaleCoroutine(Transform target, Vector3 targetScale, float duration, AnimationCurve curve)
        {
            Vector3 startScale = target.localScale;
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = curve.Evaluate(elapsed / duration);
                target.localScale = Vector3.Lerp(startScale, targetScale, progress);
                yield return null;
            }
            
            target.localScale = targetScale;
        }
    }
    
    [System.Serializable]
    public class FloatingText
    {
        public GameObject gameObject;
        public TextMeshProUGUI textMesh;
        public float startTime;
        public float duration;
        public Vector3 startPosition;
        public float moveSpeed;
        
        public bool IsExpired => Time.time - startTime >= duration;
        
        public void Update()
        {
            if (gameObject == null) return;
            
            float elapsed = Time.time - startTime;
            float progress = elapsed / duration;
            
            // Move upward
            Vector3 currentPosition = startPosition + Vector3.up * (moveSpeed * elapsed);
            gameObject.transform.position = currentPosition;
            
            // Fade out
            if (textMesh != null)
            {
                Color color = textMesh.color;
                color.a = Mathf.Lerp(1f, 0f, progress);
                textMesh.color = color;
            }
            
            // Scale effect
            float scale = Mathf.Lerp(1f, 1.2f, Mathf.Sin(progress * Mathf.PI));
            gameObject.transform.localScale = Vector3.one * scale;
        }
    }
}
