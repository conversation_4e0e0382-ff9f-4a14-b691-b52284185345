using UnityEngine;
using UnityEngine.UI;
using TMPro;
using CrystalQuest.Core;

namespace CrystalQuest.UI
{
    /// <summary>
    /// Manages all UI elements including HUD, menus, and game interface
    /// </summary>
    public class UIManager : MonoBehaviour
    {
        [Header("HUD Elements")]
        [SerializeField] private GameObject hudPanel;
        [SerializeField] private TextMeshProUGUI scoreText;
        [SerializeField] private TextMeshProUGUI crystalsText;
        [SerializeField] private TextMeshProUGUI levelText;
        [SerializeField] private Slider healthSlider;
        [SerializeField] private Image crosshair;
        
        [Header("Menu Panels")]
        [SerializeField] private GameObject mainMenuPanel;
        [SerializeField] private GameObject pauseMenuPanel;
        [SerializeField] private GameObject gameOverPanel;
        [SerializeField] private GameObject victoryPanel;
        [SerializeField] private GameObject settingsPanel;
        [SerializeField] private GameObject loadingPanel;
        
        [Header("Main Menu Buttons")]
        [SerializeField] private Button startGameButton;
        [SerializeField] private Button settingsButton;
        [SerializeField] private But<PERSON> quitButton;
        
        [Header("Pause Menu Buttons")]
        [SerializeField] private Button resumeButton;
        [SerializeField] private Button pauseSettingsButton;
        [SerializeField] private Button mainMenuButton;
        
        [Header("Game Over Buttons")]
        [SerializeField] private Button restartButton;
        [SerializeField] private Button gameOverMainMenuButton;
        
        [Header("Victory Buttons")]
        [SerializeField] private Button nextLevelButton;
        [SerializeField] private Button victoryMainMenuButton;
        
        [Header("Settings")]
        [SerializeField] private Slider masterVolumeSlider;
        [SerializeField] private Slider musicVolumeSlider;
        [SerializeField] private Slider sfxVolumeSlider;
        [SerializeField] private Slider mouseSensitivitySlider;
        [SerializeField] private Button settingsBackButton;
        
        [Header("Loading Screen")]
        [SerializeField] private TextMeshProUGUI loadingText;
        [SerializeField] private Slider loadingProgressBar;
        
        // Singleton instance
        public static UIManager Instance { get; private set; }
        
        // Animation variables
        private CanvasGroup currentPanelCanvasGroup;
        
        private void Awake()
        {
            // Implement singleton pattern
            if (Instance == null)
            {
                Instance = this;
                InitializeUI();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeUI()
        {
            // Set up button listeners
            SetupButtonListeners();
            
            // Set up slider listeners
            SetupSliderListeners();
            
            // Initialize all panels as inactive
            HideAllPanels();
            
            Debug.Log("UIManager: Initialized successfully");
        }
        
        private void Start()
        {
            // Subscribe to game manager events
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStateChanged += OnGameStateChanged;
                GameManager.Instance.OnScoreChanged += UpdateScore;
                GameManager.Instance.OnCrystalsChanged += UpdateCrystals;
            }
            
            // Show appropriate initial panel
            ShowMainMenu();
        }
        
        private void SetupButtonListeners()
        {
            // Main Menu
            if (startGameButton != null)
                startGameButton.onClick.AddListener(StartGame);
            if (settingsButton != null)
                settingsButton.onClick.AddListener(ShowSettings);
            if (quitButton != null)
                quitButton.onClick.AddListener(QuitGame);
            
            // Pause Menu
            if (resumeButton != null)
                resumeButton.onClick.AddListener(ResumeGame);
            if (pauseSettingsButton != null)
                pauseSettingsButton.onClick.AddListener(ShowSettings);
            if (mainMenuButton != null)
                mainMenuButton.onClick.AddListener(ReturnToMainMenu);
            
            // Game Over
            if (restartButton != null)
                restartButton.onClick.AddListener(RestartGame);
            if (gameOverMainMenuButton != null)
                gameOverMainMenuButton.onClick.AddListener(ReturnToMainMenu);
            
            // Victory
            if (nextLevelButton != null)
                nextLevelButton.onClick.AddListener(NextLevel);
            if (victoryMainMenuButton != null)
                victoryMainMenuButton.onClick.AddListener(ReturnToMainMenu);
            
            // Settings
            if (settingsBackButton != null)
                settingsBackButton.onClick.AddListener(HideSettings);
        }
        
        private void SetupSliderListeners()
        {
            if (masterVolumeSlider != null)
            {
                masterVolumeSlider.onValueChanged.AddListener(SetMasterVolume);
                masterVolumeSlider.value = AudioManager.Instance?.MasterVolume ?? 1f;
            }
            
            if (musicVolumeSlider != null)
            {
                musicVolumeSlider.onValueChanged.AddListener(SetMusicVolume);
                musicVolumeSlider.value = AudioManager.Instance?.MusicVolume ?? 0.7f;
            }
            
            if (sfxVolumeSlider != null)
            {
                sfxVolumeSlider.onValueChanged.AddListener(SetSFXVolume);
                sfxVolumeSlider.value = AudioManager.Instance?.SFXVolume ?? 0.8f;
            }
            
            if (mouseSensitivitySlider != null)
            {
                mouseSensitivitySlider.onValueChanged.AddListener(SetMouseSensitivity);
                mouseSensitivitySlider.value = 2f; // Default sensitivity
            }
        }
        
        private void OnGameStateChanged(GameState newState)
        {
            switch (newState)
            {
                case GameState.MainMenu:
                    ShowMainMenu();
                    break;
                case GameState.Playing:
                    ShowHUD();
                    break;
                case GameState.Paused:
                    ShowPauseMenu();
                    break;
                case GameState.GameOver:
                    ShowGameOver();
                    break;
                case GameState.Victory:
                    ShowVictory();
                    break;
                case GameState.Loading:
                    ShowLoading();
                    break;
            }
        }
        
        private void HideAllPanels()
        {
            if (hudPanel != null) hudPanel.SetActive(false);
            if (mainMenuPanel != null) mainMenuPanel.SetActive(false);
            if (pauseMenuPanel != null) pauseMenuPanel.SetActive(false);
            if (gameOverPanel != null) gameOverPanel.SetActive(false);
            if (victoryPanel != null) victoryPanel.SetActive(false);
            if (settingsPanel != null) settingsPanel.SetActive(false);
            if (loadingPanel != null) loadingPanel.SetActive(false);
        }
        
        private void ShowMainMenu()
        {
            HideAllPanels();
            if (mainMenuPanel != null)
                mainMenuPanel.SetActive(true);
        }
        
        private void ShowHUD()
        {
            HideAllPanels();
            if (hudPanel != null)
                hudPanel.SetActive(true);
            
            // Update HUD with current game state
            UpdateScore(GameManager.Instance?.PlayerScore ?? 0);
            UpdateCrystals(GameManager.Instance?.CrystalsCollected ?? 0, GameManager.Instance?.TotalCrystals ?? 0);
            UpdateLevel(GameManager.Instance?.CurrentLevel ?? 1);
        }
        
        private void ShowPauseMenu()
        {
            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(true);
        }
        
        private void ShowGameOver()
        {
            HideAllPanels();
            if (gameOverPanel != null)
                gameOverPanel.SetActive(true);
        }
        
        private void ShowVictory()
        {
            HideAllPanels();
            if (victoryPanel != null)
                victoryPanel.SetActive(true);
        }
        
        private void ShowSettings()
        {
            if (settingsPanel != null)
                settingsPanel.SetActive(true);
        }
        
        private void HideSettings()
        {
            if (settingsPanel != null)
                settingsPanel.SetActive(false);
        }
        
        private void ShowLoading()
        {
            HideAllPanels();
            if (loadingPanel != null)
                loadingPanel.SetActive(true);
        }
        
        // Button event handlers
        private void StartGame()
        {
            PlayButtonSound();
            GameManager.Instance?.StartNewGame();
        }
        
        private void ResumeGame()
        {
            PlayButtonSound();
            GameManager.Instance?.ResumeGame();
        }
        
        private void RestartGame()
        {
            PlayButtonSound();
            GameManager.Instance?.StartNewGame();
        }
        
        private void NextLevel()
        {
            PlayButtonSound();
            if (GameManager.Instance != null)
            {
                GameManager.Instance.LoadLevel(GameManager.Instance.CurrentLevel + 1);
            }
        }
        
        private void ReturnToMainMenu()
        {
            PlayButtonSound();
            GameManager.Instance?.ReturnToMainMenu();
        }
        
        private void QuitGame()
        {
            PlayButtonSound();
            GameManager.Instance?.QuitGame();
        }
        
        // Slider event handlers
        private void SetMasterVolume(float value)
        {
            if (AudioManager.Instance != null)
                AudioManager.Instance.MasterVolume = value;
        }
        
        private void SetMusicVolume(float value)
        {
            if (AudioManager.Instance != null)
                AudioManager.Instance.MusicVolume = value;
        }
        
        private void SetSFXVolume(float value)
        {
            if (AudioManager.Instance != null)
                AudioManager.Instance.SFXVolume = value;
        }
        
        private void SetMouseSensitivity(float value)
        {
            // This would typically be handled by a settings manager
            // For now, we'll just store it (you'd implement this based on your player controller)
            PlayerPrefs.SetFloat("MouseSensitivity", value);
        }
        
        // HUD update methods
        private void UpdateScore(int score)
        {
            if (scoreText != null)
                scoreText.text = $"Score: {score:N0}";
        }
        
        private void UpdateCrystals(int collected, int total)
        {
            if (crystalsText != null)
                crystalsText.text = $"Crystals: {collected}/{total}";
        }
        
        private void UpdateLevel(int level)
        {
            if (levelText != null)
                levelText.text = $"Level {level}";
        }
        
        public void UpdateHealth(float currentHealth, float maxHealth)
        {
            if (healthSlider != null)
            {
                healthSlider.value = currentHealth / maxHealth;
            }
        }
        
        public void UpdateLoadingProgress(float progress, string statusText = "Loading...")
        {
            if (loadingProgressBar != null)
                loadingProgressBar.value = progress;
            
            if (loadingText != null)
                loadingText.text = statusText;
        }
        
        private void PlayButtonSound()
        {
            AudioManager.Instance?.PlayButtonClickSound();
        }
        
        private void OnDestroy()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStateChanged -= OnGameStateChanged;
                GameManager.Instance.OnScoreChanged -= UpdateScore;
                GameManager.Instance.OnCrystalsChanged -= UpdateCrystals;
            }
        }
    }
}
