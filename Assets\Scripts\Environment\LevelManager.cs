using UnityEngine;
using CrystalQuest.Core;
using CrystalQuest.Player;

namespace CrystalQuest.Environment
{
    /// <summary>
    /// Manages level-specific functionality including crystal spawning, objectives, and level completion
    /// </summary>
    public class LevelManager : MonoBehaviour
    {
        [Header("Level Settings")]
        [SerializeField] private int levelNumber = 1;
        [SerializeField] private string levelName = "Crystal Cavern";
        [SerializeField] private float timeLimit = 300f; // 5 minutes
        [SerializeField] private bool hasTimeLimit = false;
        
        [Header("Crystal Settings")]
        [SerializeField] private Transform[] crystalSpawnPoints;
        [SerializeField] private GameObject crystalPrefab;
        [SerializeField] private int crystalsToSpawn = 5;
        [SerializeField] private bool spawnCrystalsRandomly = true;
        
        [Header("Player Spawn")]
        [SerializeField] private Transform playerSpawnPoint;
        [SerializeField] private GameObject playerPrefab;
        
        [Header("Environment")]
        [SerializeField] private Light sunLight;
        [SerializeField] private Color ambientColor = Color.gray;
        [SerializeField] private AudioClip ambientMusic;
        [SerializeField] private AudioClip[] ambientSounds;
        
        [Header("Hazards")]
        [SerializeField] private Transform[] hazardSpawnPoints;
        [SerializeField] private GameObject[] hazardPrefabs;
        
        // Private variables
        private float currentTime;
        private int crystalsSpawned = 0;
        private GameObject playerInstance;
        private Crystal[] levelCrystals;
        
        private void Start()
        {
            InitializeLevel();
        }
        
        private void InitializeLevel()
        {
            Debug.Log($"LevelManager: Initializing Level {levelNumber} - {levelName}");
            
            // Set up environment
            SetupEnvironment();
            
            // Spawn player
            SpawnPlayer();
            
            // Spawn crystals
            SpawnCrystals();
            
            // Spawn hazards
            SpawnHazards();
            
            // Initialize timer
            currentTime = timeLimit;
            
            // Notify game manager
            if (GameManager.Instance != null)
            {
                GameManager.Instance.SetTotalCrystals(crystalsSpawned);
            }
            
            // Play ambient music
            if (AudioManager.Instance != null && ambientMusic != null)
            {
                AudioManager.Instance.PlayMusic(ambientMusic);
            }
        }
        
        private void Update()
        {
            if (hasTimeLimit && GameManager.Instance != null && GameManager.Instance.CurrentState == GameState.Playing)
            {
                HandleTimeLimit();
            }
        }
        
        private void HandleTimeLimit()
        {
            currentTime -= Time.deltaTime;
            
            if (currentTime <= 0f)
            {
                currentTime = 0f;
                GameManager.Instance.GameOver();
            }
            
            // Update UI with remaining time (you'd implement this in UIManager)
            // UIManager.Instance?.UpdateTimer(currentTime);
        }
        
        private void SetupEnvironment()
        {
            // Set ambient lighting
            RenderSettings.ambientLight = ambientColor;
            
            // Configure sun light
            if (sunLight != null)
            {
                RenderSettings.sun = sunLight;
            }
            
            // Set fog for atmosphere
            RenderSettings.fog = true;
            RenderSettings.fogColor = Color.gray;
            RenderSettings.fogMode = FogMode.ExponentialSquared;
            RenderSettings.fogDensity = 0.01f;
        }
        
        private void SpawnPlayer()
        {
            if (playerSpawnPoint == null)
            {
                Debug.LogWarning("LevelManager: No player spawn point set!");
                return;
            }
            
            // Find existing player or spawn new one
            GameObject existingPlayer = GameObject.FindGameObjectWithTag("Player");
            
            if (existingPlayer != null)
            {
                // Teleport existing player
                PlayerController playerController = existingPlayer.GetComponent<PlayerController>();
                if (playerController != null)
                {
                    playerController.Teleport(playerSpawnPoint.position);
                }
                else
                {
                    existingPlayer.transform.position = playerSpawnPoint.position;
                }
                
                playerInstance = existingPlayer;
            }
            else if (playerPrefab != null)
            {
                // Spawn new player
                playerInstance = Instantiate(playerPrefab, playerSpawnPoint.position, playerSpawnPoint.rotation);
                playerInstance.tag = "Player";
            }
            
            Debug.Log($"LevelManager: Player spawned at {playerSpawnPoint.position}");
        }
        
        private void SpawnCrystals()
        {
            if (crystalPrefab == null)
            {
                Debug.LogWarning("LevelManager: No crystal prefab assigned!");
                return;
            }
            
            if (crystalSpawnPoints == null || crystalSpawnPoints.Length == 0)
            {
                Debug.LogWarning("LevelManager: No crystal spawn points set!");
                return;
            }
            
            crystalsSpawned = 0;
            levelCrystals = new Crystal[crystalsToSpawn];
            
            if (spawnCrystalsRandomly)
            {
                SpawnCrystalsRandomly();
            }
            else
            {
                SpawnCrystalsSequentially();
            }
            
            Debug.Log($"LevelManager: Spawned {crystalsSpawned} crystals");
        }
        
        private void SpawnCrystalsRandomly()
        {
            // Create a list of available spawn points
            var availableSpawnPoints = new System.Collections.Generic.List<Transform>(crystalSpawnPoints);
            
            for (int i = 0; i < crystalsToSpawn && availableSpawnPoints.Count > 0; i++)
            {
                // Pick random spawn point
                int randomIndex = Random.Range(0, availableSpawnPoints.Count);
                Transform spawnPoint = availableSpawnPoints[randomIndex];
                
                // Spawn crystal
                GameObject crystalGO = Instantiate(crystalPrefab, spawnPoint.position, spawnPoint.rotation);
                Crystal crystal = crystalGO.GetComponent<Crystal>();
                
                if (crystal != null)
                {
                    levelCrystals[i] = crystal;
                }
                
                // Remove used spawn point
                availableSpawnPoints.RemoveAt(randomIndex);
                crystalsSpawned++;
            }
        }
        
        private void SpawnCrystalsSequentially()
        {
            for (int i = 0; i < crystalsToSpawn && i < crystalSpawnPoints.Length; i++)
            {
                Transform spawnPoint = crystalSpawnPoints[i];
                
                GameObject crystalGO = Instantiate(crystalPrefab, spawnPoint.position, spawnPoint.rotation);
                Crystal crystal = crystalGO.GetComponent<Crystal>();
                
                if (crystal != null)
                {
                    levelCrystals[i] = crystal;
                }
                
                crystalsSpawned++;
            }
        }
        
        private void SpawnHazards()
        {
            if (hazardPrefabs == null || hazardPrefabs.Length == 0 || hazardSpawnPoints == null)
                return;
            
            foreach (Transform spawnPoint in hazardSpawnPoints)
            {
                if (spawnPoint == null) continue;
                
                // Randomly choose a hazard type
                GameObject hazardPrefab = hazardPrefabs[Random.Range(0, hazardPrefabs.Length)];
                
                if (hazardPrefab != null)
                {
                    Instantiate(hazardPrefab, spawnPoint.position, spawnPoint.rotation);
                }
            }
        }
        
        public void OnCrystalCollected(Crystal crystal)
        {
            // This method can be called by crystals when they're collected
            // for any level-specific logic
            Debug.Log($"LevelManager: Crystal collected in {levelName}");
        }
        
        public void RestartLevel()
        {
            // Reload the current scene
            UnityEngine.SceneManagement.SceneManager.LoadScene(UnityEngine.SceneManagement.SceneManager.GetActiveScene().name);
        }
        
        public float GetRemainingTime()
        {
            return hasTimeLimit ? currentTime : -1f;
        }
        
        public int GetCrystalsRemaining()
        {
            int remaining = 0;
            
            if (levelCrystals != null)
            {
                foreach (Crystal crystal in levelCrystals)
                {
                    if (crystal != null && !crystal.IsCollected)
                    {
                        remaining++;
                    }
                }
            }
            
            return remaining;
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw spawn points
            if (crystalSpawnPoints != null)
            {
                Gizmos.color = Color.cyan;
                foreach (Transform spawnPoint in crystalSpawnPoints)
                {
                    if (spawnPoint != null)
                    {
                        Gizmos.DrawWireSphere(spawnPoint.position, 0.5f);
                        Gizmos.DrawLine(spawnPoint.position, spawnPoint.position + Vector3.up * 2f);
                    }
                }
            }
            
            if (playerSpawnPoint != null)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireCube(playerSpawnPoint.position, Vector3.one);
                Gizmos.DrawLine(playerSpawnPoint.position, playerSpawnPoint.position + playerSpawnPoint.forward * 2f);
            }
            
            if (hazardSpawnPoints != null)
            {
                Gizmos.color = Color.red;
                foreach (Transform spawnPoint in hazardSpawnPoints)
                {
                    if (spawnPoint != null)
                    {
                        Gizmos.DrawWireCube(spawnPoint.position, Vector3.one * 0.5f);
                    }
                }
            }
        }
    }
}
