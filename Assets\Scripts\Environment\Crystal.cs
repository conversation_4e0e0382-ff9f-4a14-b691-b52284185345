using UnityEngine;
using CrystalQuest.Core;

namespace CrystalQuest.Player
{
    /// <summary>
    /// Collectible crystal that players can gather to progress through the game
    /// </summary>
    public class Crystal : MonoBehaviour, IInteractable
    {
        [Header("Crystal Settings")]
        [SerializeField] private int pointValue = 100;
        [SerializeField] private float rotationSpeed = 50f;
        [SerializeField] private float bobSpeed = 2f;
        [SerializeField] private float bobHeight = 0.5f;
        [SerializeField] private bool autoCollect = true;
        [SerializeField] private float collectDistance = 2f;
        
        [Header("Visual Effects")]
        [SerializeField] private ParticleSystem collectEffect;
        [SerializeField] private Light crystalLight;
        [SerializeField] private Renderer crystalRenderer;
        [SerializeField] private Color crystalColor = Color.cyan;
        
        [Header("Audio")]
        [SerializeField] private AudioSource audioSource;
        [SerializeField] private AudioClip collectSound;
        
        // Private variables
        private Vector3 startPosition;
        private bool isCollected = false;
        private float bobTimer = 0f;
        private Transform playerTransform;
        
        // Material property for emission
        private Material crystalMaterial;
        private static readonly int EmissionColorProperty = Shader.PropertyToID("_EmissionColor");
        
        private void Awake()
        {
            startPosition = transform.position;
            
            // Get or create audio source
            if (audioSource == null)
            {
                audioSource = GetComponent<AudioSource>();
                if (audioSource == null)
                {
                    audioSource = gameObject.AddComponent<AudioSource>();
                }
            }
            
            // Configure audio source
            audioSource.playOnAwake = false;
            audioSource.spatialBlend = 1f; // 3D sound
            
            // Set up material for emission effects
            if (crystalRenderer != null)
            {
                crystalMaterial = crystalRenderer.material;
                if (crystalMaterial.HasProperty(EmissionColorProperty))
                {
                    crystalMaterial.SetColor(EmissionColorProperty, crystalColor * 0.5f);
                }
            }
            
            // Set up light color
            if (crystalLight != null)
            {
                crystalLight.color = crystalColor;
            }
            
            // Find player for auto-collect
            if (autoCollect)
            {
                GameObject player = GameObject.FindGameObjectWithTag("Player");
                if (player != null)
                {
                    playerTransform = player.transform;
                }
            }
        }
        
        private void Start()
        {
            // Add random offset to bob timer for variety
            bobTimer = Random.Range(0f, Mathf.PI * 2f);
        }
        
        private void Update()
        {
            if (isCollected) return;
            
            // Rotate the crystal
            transform.Rotate(Vector3.up, rotationSpeed * Time.deltaTime);
            
            // Bob up and down
            bobTimer += bobSpeed * Time.deltaTime;
            float bobOffset = Mathf.Sin(bobTimer) * bobHeight;
            transform.position = startPosition + Vector3.up * bobOffset;
            
            // Pulse the light intensity
            if (crystalLight != null)
            {
                float pulseIntensity = 1f + Mathf.Sin(bobTimer * 2f) * 0.3f;
                crystalLight.intensity = pulseIntensity;
            }
            
            // Check for auto-collect
            if (autoCollect && playerTransform != null)
            {
                float distanceToPlayer = Vector3.Distance(transform.position, playerTransform.position);
                if (distanceToPlayer <= collectDistance)
                {
                    Collect();
                }
            }
        }
        
        public void Collect()
        {
            if (isCollected) return;
            
            isCollected = true;
            
            // Play collection effects
            PlayCollectionEffects();
            
            // Notify game manager
            if (GameManager.Instance != null)
            {
                GameManager.Instance.CollectCrystal();
            }
            
            // Play audio
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.PlayCrystalCollectSound();
            }
            else if (audioSource != null && collectSound != null)
            {
                audioSource.PlayOneShot(collectSound);
            }
            
            // Disable visual components immediately
            if (crystalRenderer != null)
            {
                crystalRenderer.enabled = false;
            }
            
            if (crystalLight != null)
            {
                crystalLight.enabled = false;
            }
            
            // Disable collider
            Collider crystalCollider = GetComponent<Collider>();
            if (crystalCollider != null)
            {
                crystalCollider.enabled = false;
            }
            
            // Destroy after effects finish
            Destroy(gameObject, 2f);
        }
        
        private void PlayCollectionEffects()
        {
            // Play particle effect
            if (collectEffect != null)
            {
                collectEffect.Play();
            }
            else
            {
                // Create a simple particle effect if none is assigned
                CreateSimpleCollectionEffect();
            }
        }
        
        private void CreateSimpleCollectionEffect()
        {
            // Create a simple sparkle effect
            GameObject effectGO = new GameObject("CrystalCollectEffect");
            effectGO.transform.position = transform.position;
            
            ParticleSystem particles = effectGO.AddComponent<ParticleSystem>();
            var main = particles.main;
            main.startLifetime = 1f;
            main.startSpeed = 5f;
            main.startSize = 0.1f;
            main.startColor = crystalColor;
            main.maxParticles = 20;
            
            var emission = particles.emission;
            emission.SetBursts(new ParticleSystem.Burst[]
            {
                new ParticleSystem.Burst(0f, 20)
            });
            
            var shape = particles.shape;
            shape.shapeType = ParticleSystemShapeType.Sphere;
            shape.radius = 0.5f;
            
            var velocityOverLifetime = particles.velocityOverLifetime;
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.Local;
            velocityOverLifetime.radial = new ParticleSystem.MinMaxCurve(2f);
            
            // Destroy effect after it finishes
            Destroy(effectGO, 2f);
        }
        
        public void Interact()
        {
            Collect();
        }
        
        private void OnTriggerEnter(Collider other)
        {
            if (autoCollect && other.CompareTag("Player"))
            {
                Collect();
            }
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw collection radius
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, collectDistance);
            
            // Draw bob range
            Gizmos.color = Color.green;
            Vector3 startPos = Application.isPlaying ? startPosition : transform.position;
            Gizmos.DrawLine(startPos + Vector3.up * bobHeight, startPos - Vector3.up * bobHeight);
        }
        
        // Public properties for external access
        public int PointValue => pointValue;
        public bool IsCollected => isCollected;
        public Color CrystalColor => crystalColor;
    }
}
