using UnityEngine;
using System;

namespace CrystalQuest.Core
{
    /// <summary>
    /// Centralized input management system with customizable key bindings
    /// </summary>
    public class InputManager : MonoBehaviour
    {
        [Header("Movement Keys")]
        [SerializeField] private KeyCode forwardKey = KeyCode.W;
        [SerializeField] private KeyCode backwardKey = KeyCode.S;
        [SerializeField] private KeyCode leftKey = KeyCode.A;
        [SerializeField] private KeyCode rightKey = KeyCode.D;
        [SerializeField] private KeyCode jumpKey = KeyCode.Space;
        [SerializeField] private KeyCode runKey = KeyCode.LeftShift;
        
        [Header("Action Keys")]
        [SerializeField] private KeyCode interactKey = KeyCode.E;
        [SerializeField] private KeyCode pauseKey = KeyCode.Escape;
        [SerializeField] private KeyCode inventoryKey = KeyCode.Tab;
        
        [Header("Mouse Settings")]
        [SerializeField] private float mouseSensitivity = 2f;
        [SerializeField] private bool invertMouseY = false;
        
        // Singleton instance
        public static InputManager Instance { get; private set; }
        
        // Input events
        public static event Action OnJumpPressed;
        public static event Action OnInteractPressed;
        public static event Action OnPausePressed;
        public static event Action OnInventoryPressed;
        
        // Input state properties
        public Vector2 MovementInput { get; private set; }
        public Vector2 MouseInput { get; private set; }
        public bool IsRunning { get; private set; }
        public bool IsJumping { get; private set; }
        
        // Key binding properties
        public KeyCode ForwardKey => forwardKey;
        public KeyCode BackwardKey => backwardKey;
        public KeyCode LeftKey => leftKey;
        public KeyCode RightKey => rightKey;
        public KeyCode JumpKey => jumpKey;
        public KeyCode RunKey => runKey;
        public KeyCode InteractKey => interactKey;
        public KeyCode PauseKey => pauseKey;
        public KeyCode InventoryKey => inventoryKey;
        
        public float MouseSensitivity 
        { 
            get => mouseSensitivity; 
            set => mouseSensitivity = Mathf.Clamp(value, 0.1f, 10f); 
        }
        
        public bool InvertMouseY 
        { 
            get => invertMouseY; 
            set => invertMouseY = value; 
        }
        
        private void Awake()
        {
            // Implement singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                LoadInputSettings();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Update()
        {
            // Only process input if game is playing
            if (GameManager.Instance != null && GameManager.Instance.CurrentState != GameState.Playing)
            {
                // Still allow pause input in other states
                if (Input.GetKeyDown(pauseKey))
                {
                    OnPausePressed?.Invoke();
                }
                return;
            }
            
            HandleMovementInput();
            HandleMouseInput();
            HandleActionInput();
        }
        
        private void HandleMovementInput()
        {
            // Get movement input
            float horizontal = 0f;
            float vertical = 0f;
            
            if (Input.GetKey(forwardKey)) vertical += 1f;
            if (Input.GetKey(backwardKey)) vertical -= 1f;
            if (Input.GetKey(rightKey)) horizontal += 1f;
            if (Input.GetKey(leftKey)) horizontal -= 1f;
            
            MovementInput = new Vector2(horizontal, vertical);
            
            // Normalize diagonal movement
            if (MovementInput.magnitude > 1f)
            {
                MovementInput = MovementInput.normalized;
            }
            
            // Check running state
            IsRunning = Input.GetKey(runKey);
            
            // Check jumping (only on key down for single jump)
            if (Input.GetKeyDown(jumpKey))
            {
                IsJumping = true;
                OnJumpPressed?.Invoke();
            }
            else
            {
                IsJumping = false;
            }
        }
        
        private void HandleMouseInput()
        {
            float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
            float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;
            
            if (invertMouseY)
            {
                mouseY = -mouseY;
            }
            
            MouseInput = new Vector2(mouseX, mouseY);
        }
        
        private void HandleActionInput()
        {
            // Interact key
            if (Input.GetKeyDown(interactKey))
            {
                OnInteractPressed?.Invoke();
            }
            
            // Pause key
            if (Input.GetKeyDown(pauseKey))
            {
                OnPausePressed?.Invoke();
            }
            
            // Inventory key
            if (Input.GetKeyDown(inventoryKey))
            {
                OnInventoryPressed?.Invoke();
            }
        }
        
        public void SetKeyBinding(string action, KeyCode newKey)
        {
            switch (action.ToLower())
            {
                case "forward":
                    forwardKey = newKey;
                    break;
                case "backward":
                    backwardKey = newKey;
                    break;
                case "left":
                    leftKey = newKey;
                    break;
                case "right":
                    rightKey = newKey;
                    break;
                case "jump":
                    jumpKey = newKey;
                    break;
                case "run":
                    runKey = newKey;
                    break;
                case "interact":
                    interactKey = newKey;
                    break;
                case "pause":
                    pauseKey = newKey;
                    break;
                case "inventory":
                    inventoryKey = newKey;
                    break;
                default:
                    Debug.LogWarning($"InputManager: Unknown action '{action}'");
                    return;
            }
            
            SaveInputSettings();
            Debug.Log($"InputManager: Set {action} key to {newKey}");
        }
        
        public KeyCode GetKeyBinding(string action)
        {
            switch (action.ToLower())
            {
                case "forward": return forwardKey;
                case "backward": return backwardKey;
                case "left": return leftKey;
                case "right": return rightKey;
                case "jump": return jumpKey;
                case "run": return runKey;
                case "interact": return interactKey;
                case "pause": return pauseKey;
                case "inventory": return inventoryKey;
                default:
                    Debug.LogWarning($"InputManager: Unknown action '{action}'");
                    return KeyCode.None;
            }
        }
        
        public void ResetToDefaults()
        {
            forwardKey = KeyCode.W;
            backwardKey = KeyCode.S;
            leftKey = KeyCode.A;
            rightKey = KeyCode.D;
            jumpKey = KeyCode.Space;
            runKey = KeyCode.LeftShift;
            interactKey = KeyCode.E;
            pauseKey = KeyCode.Escape;
            inventoryKey = KeyCode.Tab;
            mouseSensitivity = 2f;
            invertMouseY = false;
            
            SaveInputSettings();
            Debug.Log("InputManager: Reset to default settings");
        }
        
        private void LoadInputSettings()
        {
            // Load key bindings from PlayerPrefs
            forwardKey = (KeyCode)PlayerPrefs.GetInt("Input_Forward", (int)KeyCode.W);
            backwardKey = (KeyCode)PlayerPrefs.GetInt("Input_Backward", (int)KeyCode.S);
            leftKey = (KeyCode)PlayerPrefs.GetInt("Input_Left", (int)KeyCode.A);
            rightKey = (KeyCode)PlayerPrefs.GetInt("Input_Right", (int)KeyCode.D);
            jumpKey = (KeyCode)PlayerPrefs.GetInt("Input_Jump", (int)KeyCode.Space);
            runKey = (KeyCode)PlayerPrefs.GetInt("Input_Run", (int)KeyCode.LeftShift);
            interactKey = (KeyCode)PlayerPrefs.GetInt("Input_Interact", (int)KeyCode.E);
            pauseKey = (KeyCode)PlayerPrefs.GetInt("Input_Pause", (int)KeyCode.Escape);
            inventoryKey = (KeyCode)PlayerPrefs.GetInt("Input_Inventory", (int)KeyCode.Tab);
            
            // Load mouse settings
            mouseSensitivity = PlayerPrefs.GetFloat("Input_MouseSensitivity", 2f);
            invertMouseY = PlayerPrefs.GetInt("Input_InvertMouseY", 0) == 1;
            
            Debug.Log("InputManager: Settings loaded from PlayerPrefs");
        }
        
        private void SaveInputSettings()
        {
            // Save key bindings to PlayerPrefs
            PlayerPrefs.SetInt("Input_Forward", (int)forwardKey);
            PlayerPrefs.SetInt("Input_Backward", (int)backwardKey);
            PlayerPrefs.SetInt("Input_Left", (int)leftKey);
            PlayerPrefs.SetInt("Input_Right", (int)rightKey);
            PlayerPrefs.SetInt("Input_Jump", (int)jumpKey);
            PlayerPrefs.SetInt("Input_Run", (int)runKey);
            PlayerPrefs.SetInt("Input_Interact", (int)interactKey);
            PlayerPrefs.SetInt("Input_Pause", (int)pauseKey);
            PlayerPrefs.SetInt("Input_Inventory", (int)inventoryKey);
            
            // Save mouse settings
            PlayerPrefs.SetFloat("Input_MouseSensitivity", mouseSensitivity);
            PlayerPrefs.SetInt("Input_InvertMouseY", invertMouseY ? 1 : 0);
            
            PlayerPrefs.Save();
        }
        
        // Utility methods for checking input states
        public bool IsMoving()
        {
            return MovementInput.magnitude > 0.1f;
        }
        
        public bool IsMovingForward()
        {
            return MovementInput.y > 0.1f;
        }
        
        public bool IsMovingBackward()
        {
            return MovementInput.y < -0.1f;
        }
        
        public bool IsStrafing()
        {
            return Mathf.Abs(MovementInput.x) > 0.1f;
        }
        
        private void OnDestroy()
        {
            SaveInputSettings();
        }
    }
}
