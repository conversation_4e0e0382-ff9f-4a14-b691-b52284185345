using UnityEngine;
using CrystalQuest.Core;
using System.Collections.Generic;

namespace CrystalQuest.Gameplay
{
    /// <summary>
    /// Manages scoring system, achievements, and score multipliers
    /// </summary>
    public class ScoreManager : MonoBehaviour
    {
        [Header("Score Settings")]
        [SerializeField] private int baseScore = 0;
        [SerializeField] private float scoreMultiplier = 1f;
        [SerializeField] private float maxMultiplier = 5f;
        [SerializeField] private float multiplierDecayRate = 0.5f;
        [SerializeField] private float multiplierIncrement = 0.1f;
        
        [Header("Score Values")]
        [SerializeField] private int crystalScore = 100;
        [SerializeField] private int enemyScore = 50;
        [SerializeField] private int levelCompleteScore = 500;
        [SerializeField] private int timeBonus = 10; // per second remaining
        [SerializeField] private int healthBonus = 5; // per health point
        
        [Header("Combo System")]
        [SerializeField] private bool enableComboSystem = true;
        [SerializeField] private float comboTimeWindow = 3f;
        [SerializeField] private int comboThreshold = 3;
        [SerializeField] private float comboMultiplier = 1.5f;
        
        // Singleton instance
        public static ScoreManager Instance { get; private set; }
        
        // Events
        public System.Action<int> OnScoreChanged;
        public System.Action<float> OnMultiplierChanged;
        public System.Action<int> OnComboChanged;
        public System.Action<ScoreEvent> OnScoreEvent;
        
        // Private variables
        private int currentScore;
        private int currentCombo = 0;
        private float lastScoreTime;
        private List<ScoreEvent> recentScoreEvents = new List<ScoreEvent>();
        private Dictionary<ScoreType, int> scoreBreakdown = new Dictionary<ScoreType, int>();
        
        // Properties
        public int CurrentScore => currentScore;
        public float CurrentMultiplier => scoreMultiplier;
        public int CurrentCombo => currentCombo;
        public Dictionary<ScoreType, int> ScoreBreakdown => new Dictionary<ScoreType, int>(scoreBreakdown);
        
        private void Awake()
        {
            // Implement singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeScoreManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeScoreManager()
        {
            currentScore = baseScore;
            
            // Initialize score breakdown
            foreach (ScoreType scoreType in System.Enum.GetValues(typeof(ScoreType)))
            {
                scoreBreakdown[scoreType] = 0;
            }
            
            Debug.Log("ScoreManager: Initialized successfully");
        }
        
        private void Start()
        {
            // Subscribe to game events
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStateChanged += OnGameStateChanged;
            }
        }
        
        private void Update()
        {
            UpdateMultiplier();
            UpdateCombo();
        }
        
        private void UpdateMultiplier()
        {
            // Decay multiplier over time
            if (scoreMultiplier > 1f)
            {
                scoreMultiplier -= multiplierDecayRate * Time.deltaTime;
                scoreMultiplier = Mathf.Max(1f, scoreMultiplier);
                OnMultiplierChanged?.Invoke(scoreMultiplier);
            }
        }
        
        private void UpdateCombo()
        {
            if (enableComboSystem && currentCombo > 0)
            {
                // Check if combo should break
                if (Time.time - lastScoreTime > comboTimeWindow)
                {
                    BreakCombo();
                }
            }
        }
        
        public void AddScore(int points, ScoreType scoreType, Vector3 position = default)
        {
            if (points <= 0) return;
            
            // Apply multiplier
            int finalScore = Mathf.RoundToInt(points * scoreMultiplier);
            
            // Apply combo bonus
            if (enableComboSystem && currentCombo >= comboThreshold)
            {
                finalScore = Mathf.RoundToInt(finalScore * comboMultiplier);
            }
            
            // Add to total score
            currentScore += finalScore;
            
            // Update score breakdown
            if (scoreBreakdown.ContainsKey(scoreType))
            {
                scoreBreakdown[scoreType] += finalScore;
            }
            
            // Update combo
            if (enableComboSystem)
            {
                UpdateComboSystem(scoreType);
            }
            
            // Increase multiplier
            IncreaseMultiplier();
            
            // Create score event
            ScoreEvent scoreEvent = new ScoreEvent
            {
                points = finalScore,
                originalPoints = points,
                scoreType = scoreType,
                position = position,
                timestamp = Time.time,
                multiplier = scoreMultiplier,
                combo = currentCombo
            };
            
            recentScoreEvents.Add(scoreEvent);
            
            // Clean up old events
            CleanupOldScoreEvents();
            
            // Notify listeners
            OnScoreChanged?.Invoke(currentScore);
            OnScoreEvent?.Invoke(scoreEvent);
            
            Debug.Log($"ScoreManager: Added {finalScore} points ({scoreType}). Total: {currentScore}");
        }
        
        private void UpdateComboSystem(ScoreType scoreType)
        {
            // Only certain score types contribute to combo
            if (scoreType == ScoreType.Crystal || scoreType == ScoreType.Enemy)
            {
                currentCombo++;
                lastScoreTime = Time.time;
                OnComboChanged?.Invoke(currentCombo);
            }
        }
        
        private void BreakCombo()
        {
            if (currentCombo > 0)
            {
                Debug.Log($"ScoreManager: Combo broken at {currentCombo}");
                currentCombo = 0;
                OnComboChanged?.Invoke(currentCombo);
            }
        }
        
        private void IncreaseMultiplier()
        {
            scoreMultiplier += multiplierIncrement;
            scoreMultiplier = Mathf.Min(scoreMultiplier, maxMultiplier);
            OnMultiplierChanged?.Invoke(scoreMultiplier);
        }
        
        private void CleanupOldScoreEvents()
        {
            float cutoffTime = Time.time - 10f; // Keep events for 10 seconds
            recentScoreEvents.RemoveAll(e => e.timestamp < cutoffTime);
        }
        
        // Specific scoring methods
        public void AddCrystalScore(Vector3 position = default)
        {
            AddScore(crystalScore, ScoreType.Crystal, position);
        }
        
        public void AddEnemyScore(Vector3 position = default)
        {
            AddScore(enemyScore, ScoreType.Enemy, position);
        }
        
        public void AddLevelCompleteScore()
        {
            AddScore(levelCompleteScore, ScoreType.LevelComplete);
        }
        
        public void AddTimeBonus(float timeRemaining)
        {
            int bonus = Mathf.RoundToInt(timeRemaining * timeBonus);
            AddScore(bonus, ScoreType.TimeBonus);
        }
        
        public void AddHealthBonus(int healthRemaining)
        {
            int bonus = healthRemaining * healthBonus;
            AddScore(bonus, ScoreType.HealthBonus);
        }
        
        public void AddCustomScore(int points, string description, Vector3 position = default)
        {
            AddScore(points, ScoreType.Custom, position);
        }
        
        public void ResetScore()
        {
            currentScore = baseScore;
            scoreMultiplier = 1f;
            currentCombo = 0;
            recentScoreEvents.Clear();
            
            // Reset breakdown
            foreach (ScoreType scoreType in System.Enum.GetValues(typeof(ScoreType)))
            {
                scoreBreakdown[scoreType] = 0;
            }
            
            OnScoreChanged?.Invoke(currentScore);
            OnMultiplierChanged?.Invoke(scoreMultiplier);
            OnComboChanged?.Invoke(currentCombo);
            
            Debug.Log("ScoreManager: Score reset");
        }
        
        public void SetScore(int newScore)
        {
            currentScore = newScore;
            OnScoreChanged?.Invoke(currentScore);
        }
        
        public List<ScoreEvent> GetRecentScoreEvents(float timeWindow = 5f)
        {
            float cutoffTime = Time.time - timeWindow;
            return recentScoreEvents.FindAll(e => e.timestamp >= cutoffTime);
        }
        
        public int GetScoreByType(ScoreType scoreType)
        {
            return scoreBreakdown.ContainsKey(scoreType) ? scoreBreakdown[scoreType] : 0;
        }
        
        public float GetScoreRate()
        {
            // Calculate score per second over the last minute
            float timeWindow = 60f;
            float cutoffTime = Time.time - timeWindow;
            
            var recentEvents = recentScoreEvents.FindAll(e => e.timestamp >= cutoffTime);
            int totalScore = 0;
            
            foreach (var scoreEvent in recentEvents)
            {
                totalScore += scoreEvent.points;
            }
            
            return totalScore / timeWindow;
        }
        
        private void OnGameStateChanged(GameState newState)
        {
            switch (newState)
            {
                case GameState.Playing:
                    // Resume scoring
                    break;
                case GameState.Paused:
                    // Pause combo decay
                    break;
                case GameState.GameOver:
                    // Final score calculation
                    CalculateFinalScore();
                    break;
                case GameState.MainMenu:
                    // Reset score for new game
                    ResetScore();
                    break;
            }
        }
        
        private void CalculateFinalScore()
        {
            // Add any final bonuses
            if (GameManager.Instance != null)
            {
                // Time bonus if level has time limit
                // Health bonus based on remaining health
                // etc.
            }
            
            Debug.Log($"ScoreManager: Final score: {currentScore}");
        }
        
        private void OnDestroy()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStateChanged -= OnGameStateChanged;
            }
        }
        
        private void OnValidate()
        {
            maxMultiplier = Mathf.Max(1f, maxMultiplier);
            multiplierDecayRate = Mathf.Max(0f, multiplierDecayRate);
            multiplierIncrement = Mathf.Max(0f, multiplierIncrement);
            comboTimeWindow = Mathf.Max(0.1f, comboTimeWindow);
            comboThreshold = Mathf.Max(1, comboThreshold);
            comboMultiplier = Mathf.Max(1f, comboMultiplier);
        }
    }
    
    [System.Serializable]
    public class ScoreEvent
    {
        public int points;
        public int originalPoints;
        public ScoreType scoreType;
        public Vector3 position;
        public float timestamp;
        public float multiplier;
        public int combo;
    }
    
    public enum ScoreType
    {
        Crystal,
        Enemy,
        LevelComplete,
        TimeBonus,
        HealthBonus,
        Combo,
        Custom
    }
}
