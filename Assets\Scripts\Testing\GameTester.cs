using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using CrystalQuest.Core;
using CrystalQuest.Player;

namespace CrystalQuest.Testing
{
    /// <summary>
    /// Automated testing system for game functionality and regression testing
    /// </summary>
    public class GameTester : MonoBehaviour
    {
        [Header("Test Settings")]
        [SerializeField] private bool runTestsOnStart = false;
        [SerializeField] private bool enableDebugLogs = true;
        [SerializeField] private float testTimeout = 30f;
        
        [Header("Test Categories")]
        [SerializeField] private bool testGameManager = true;
        [SerializeField] private bool testPlayerController = true;
        [SerializeField] private bool testAudioSystem = true;
        [SerializeField] private bool testUISystem = true;
        [SerializeField] private bool testSaveSystem = true;
        
        [Header("Automated Testing")]
        [SerializeField] private bool enableAutomatedPlaytest = false;
        [SerializeField] private float playtestDuration = 60f;
        [SerializeField] private Vector3[] testWaypoints;
        
        // Test results
        private List<TestResult> testResults = new List<TestResult>();
        private bool isRunningTests = false;
        private Coroutine currentTestCoroutine;
        
        // Test components
        private GameObject testPlayer;
        private PlayerController testPlayerController;
        
        private void Start()
        {
            if (runTestsOnStart)
            {
                StartCoroutine(RunAllTestsCoroutine());
            }
        }
        
        private void Update()
        {
            // Test hotkeys
            if (Input.GetKeyDown(KeyCode.F5))
            {
                StartCoroutine(RunAllTestsCoroutine());
            }
            
            if (Input.GetKeyDown(KeyCode.F6))
            {
                StartAutomatedPlaytest();
            }
            
            if (Input.GetKeyDown(KeyCode.F7))
            {
                LogTestResults();
            }
        }
        
        [ContextMenu("Run All Tests")]
        public void RunAllTests()
        {
            if (!isRunningTests)
            {
                StartCoroutine(RunAllTestsCoroutine());
            }
        }
        
        private IEnumerator RunAllTestsCoroutine()
        {
            isRunningTests = true;
            testResults.Clear();
            
            LogTest("Starting comprehensive game tests...");
            
            if (testGameManager)
            {
                yield return StartCoroutine(TestGameManagerCoroutine());
            }
            
            if (testPlayerController)
            {
                yield return StartCoroutine(TestPlayerControllerCoroutine());
            }
            
            if (testAudioSystem)
            {
                yield return StartCoroutine(TestAudioSystemCoroutine());
            }
            
            if (testUISystem)
            {
                yield return StartCoroutine(TestUISystemCoroutine());
            }
            
            if (testSaveSystem)
            {
                yield return StartCoroutine(TestSaveSystemCoroutine());
            }
            
            LogTestResults();
            isRunningTests = false;
        }
        
        private IEnumerator TestGameManagerCoroutine()
        {
            LogTest("Testing GameManager...");
            
            // Test singleton
            bool singletonTest = GameManager.Instance != null;
            AddTestResult("GameManager Singleton", singletonTest, "GameManager instance should exist");
            
            if (GameManager.Instance != null)
            {
                // Test state changes
                GameState initialState = GameManager.Instance.CurrentState;
                GameManager.Instance.ChangeGameState(GameState.Paused);
                bool stateChangeTest = GameManager.Instance.CurrentState == GameState.Paused;
                AddTestResult("GameManager State Change", stateChangeTest, "State should change to Paused");
                
                // Restore initial state
                GameManager.Instance.ChangeGameState(initialState);
                
                // Test score system
                int initialScore = GameManager.Instance.PlayerScore;
                GameManager.Instance.AddScore(100);
                bool scoreTest = GameManager.Instance.PlayerScore == initialScore + 100;
                AddTestResult("GameManager Score", scoreTest, "Score should increase by 100");
            }
            
            yield return new WaitForSeconds(0.1f);
        }
        
        private IEnumerator TestPlayerControllerCoroutine()
        {
            LogTest("Testing PlayerController...");
            
            // Find or create test player
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player == null)
            {
                player = CreateTestPlayer();
            }
            
            PlayerController playerController = player.GetComponent<PlayerController>();
            bool playerControllerTest = playerController != null;
            AddTestResult("PlayerController Component", playerControllerTest, "Player should have PlayerController component");
            
            if (playerController != null)
            {
                // Test movement
                Vector3 initialPosition = player.transform.position;
                
                // Simulate movement input (this would require modifying PlayerController to accept test input)
                yield return new WaitForSeconds(1f);
                
                // Test teleport function
                Vector3 testPosition = initialPosition + Vector3.forward * 5f;
                playerController.Teleport(testPosition);
                bool teleportTest = Vector3.Distance(player.transform.position, testPosition) < 0.1f;
                AddTestResult("PlayerController Teleport", teleportTest, "Player should teleport to target position");
            }
            
            yield return new WaitForSeconds(0.1f);
        }
        
        private IEnumerator TestAudioSystemCoroutine()
        {
            LogTest("Testing Audio System...");
            
            // Test AudioManager singleton
            bool audioManagerTest = AudioManager.Instance != null;
            AddTestResult("AudioManager Singleton", audioManagerTest, "AudioManager instance should exist");
            
            if (AudioManager.Instance != null)
            {
                // Test volume controls
                float initialVolume = AudioManager.Instance.MasterVolume;
                AudioManager.Instance.MasterVolume = 0.5f;
                bool volumeTest = Mathf.Approximately(AudioManager.Instance.MasterVolume, 0.5f);
                AddTestResult("AudioManager Volume Control", volumeTest, "Master volume should be settable");
                
                // Restore initial volume
                AudioManager.Instance.MasterVolume = initialVolume;
                
                // Test sound effects (basic functionality)
                AudioManager.Instance.PlayButtonClickSound();
                bool sfxTest = true; // Assume success if no exceptions
                AddTestResult("AudioManager SFX", sfxTest, "Sound effects should play without errors");
            }
            
            yield return new WaitForSeconds(0.1f);
        }
        
        private IEnumerator TestUISystemCoroutine()
        {
            LogTest("Testing UI System...");
            
            // Test UIManager
            bool uiManagerTest = UIManager.Instance != null;
            AddTestResult("UIManager Singleton", uiManagerTest, "UIManager instance should exist");
            
            // Test canvas components
            Canvas[] canvases = FindObjectsOfType<Canvas>();
            bool canvasTest = canvases.Length > 0;
            AddTestResult("UI Canvas", canvasTest, "At least one Canvas should exist");
            
            // Test event system
            UnityEngine.EventSystems.EventSystem eventSystem = FindObjectOfType<UnityEngine.EventSystems.EventSystem>();
            bool eventSystemTest = eventSystem != null;
            AddTestResult("UI EventSystem", eventSystemTest, "EventSystem should exist for UI interaction");
            
            yield return new WaitForSeconds(0.1f);
        }
        
        private IEnumerator TestSaveSystemCoroutine()
        {
            LogTest("Testing Save System...");
            
            // Test SaveSystem singleton
            bool saveSystemTest = SaveSystem.Instance != null;
            AddTestResult("SaveSystem Singleton", saveSystemTest, "SaveSystem instance should exist");
            
            if (SaveSystem.Instance != null)
            {
                // Test save functionality
                try
                {
                    SaveSystem.Instance.SaveGame();
                    bool saveTest = SaveSystem.Instance.HasSaveFile;
                    AddTestResult("SaveSystem Save", saveTest, "Game should save successfully");
                    
                    // Test load functionality
                    bool loadTest = SaveSystem.Instance.LoadGame();
                    AddTestResult("SaveSystem Load", loadTest, "Game should load successfully");
                }
                catch (System.Exception e)
                {
                    AddTestResult("SaveSystem Save/Load", false, $"Save/Load failed: {e.Message}");
                }
            }
            
            yield return new WaitForSeconds(0.1f);
        }
        
        private GameObject CreateTestPlayer()
        {
            GameObject player = new GameObject("TestPlayer");
            player.tag = "Player";
            
            // Add CharacterController
            CharacterController controller = player.AddComponent<CharacterController>();
            controller.height = 2f;
            controller.radius = 0.5f;
            
            // Add PlayerController
            player.AddComponent<PlayerController>();
            
            // Position player
            player.transform.position = Vector3.zero;
            
            return player;
        }
        
        public void StartAutomatedPlaytest()
        {
            if (enableAutomatedPlaytest && !isRunningTests)
            {
                StartCoroutine(AutomatedPlaytestCoroutine());
            }
        }
        
        private IEnumerator AutomatedPlaytestCoroutine()
        {
            LogTest("Starting automated playtest...");
            isRunningTests = true;
            
            // Find player
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player == null)
            {
                LogTest("No player found for automated playtest");
                isRunningTests = false;
                yield break;
            }
            
            PlayerController playerController = player.GetComponent<PlayerController>();
            if (playerController == null)
            {
                LogTest("Player has no PlayerController for automated playtest");
                isRunningTests = false;
                yield break;
            }
            
            float startTime = Time.time;
            int waypointIndex = 0;
            
            while (Time.time - startTime < playtestDuration)
            {
                // Move to waypoints
                if (testWaypoints != null && testWaypoints.Length > 0)
                {
                    Vector3 targetWaypoint = testWaypoints[waypointIndex];
                    playerController.Teleport(targetWaypoint);
                    
                    waypointIndex = (waypointIndex + 1) % testWaypoints.Length;
                    
                    // Wait at waypoint
                    yield return new WaitForSeconds(2f);
                }
                else
                {
                    // Random movement
                    Vector3 randomPosition = player.transform.position + Random.insideUnitSphere * 10f;
                    randomPosition.y = player.transform.position.y; // Keep same height
                    playerController.Teleport(randomPosition);
                    
                    yield return new WaitForSeconds(1f);
                }
                
                // Simulate random actions
                if (Random.value < 0.3f)
                {
                    // Simulate jump (would need to modify PlayerController for test input)
                    LogTest("Simulated jump action");
                }
                
                yield return new WaitForSeconds(0.1f);
            }
            
            LogTest("Automated playtest completed");
            isRunningTests = false;
        }
        
        private void AddTestResult(string testName, bool passed, string description)
        {
            TestResult result = new TestResult
            {
                testName = testName,
                passed = passed,
                description = description,
                timestamp = System.DateTime.Now
            };
            
            testResults.Add(result);
            
            if (enableDebugLogs)
            {
                string status = passed ? "PASS" : "FAIL";
                LogTest($"[{status}] {testName}: {description}");
            }
        }
        
        private void LogTestResults()
        {
            int passedTests = 0;
            int totalTests = testResults.Count;
            
            LogTest("=== TEST RESULTS ===");
            
            foreach (TestResult result in testResults)
            {
                string status = result.passed ? "PASS" : "FAIL";
                LogTest($"[{status}] {result.testName}");
                
                if (result.passed)
                {
                    passedTests++;
                }
            }
            
            LogTest($"Tests Passed: {passedTests}/{totalTests} ({(float)passedTests / totalTests * 100:F1}%)");
            LogTest("===================");
        }
        
        private void LogTest(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[GAME_TESTER] {message}");
            }
        }
        
        public List<TestResult> GetTestResults()
        {
            return new List<TestResult>(testResults);
        }
        
        public bool AllTestsPassed()
        {
            foreach (TestResult result in testResults)
            {
                if (!result.passed)
                {
                    return false;
                }
            }
            return testResults.Count > 0;
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw test waypoints
            if (testWaypoints != null)
            {
                Gizmos.color = Color.blue;
                for (int i = 0; i < testWaypoints.Length; i++)
                {
                    Gizmos.DrawWireSphere(testWaypoints[i], 1f);
                    
                    if (i < testWaypoints.Length - 1)
                    {
                        Gizmos.DrawLine(testWaypoints[i], testWaypoints[i + 1]);
                    }
                    else if (testWaypoints.Length > 2)
                    {
                        Gizmos.DrawLine(testWaypoints[i], testWaypoints[0]);
                    }
                }
            }
        }
    }
    
    [System.Serializable]
    public struct TestResult
    {
        public string testName;
        public bool passed;
        public string description;
        public System.DateTime timestamp;
    }
}
