using UnityEngine;
using CrystalQuest.Core;

namespace CrystalQuest.Player
{
    /// <summary>
    /// Manages player health, damage, and death mechanics
    /// </summary>
    public class PlayerHealth : MonoBehaviour
    {
        [Header("Health Settings")]
        [SerializeField] private int maxHealth = 100;
        [SerializeField] private int currentHealth;
        [SerializeField] private bool invulnerable = false;
        [SerializeField] private float invulnerabilityDuration = 2f;
        
        [Header("Regeneration")]
        [SerializeField] private bool enableHealthRegen = true;
        [SerializeField] private float regenRate = 5f; // health per second
        [SerializeField] private float regenDelay = 3f; // delay after taking damage
        
        [Header("Visual Effects")]
        [SerializeField] private Renderer playerRenderer;
        [SerializeField] private Color damageColor = Color.red;
        [SerializeField] private float damageFlashDuration = 0.2f;
        
        [Header("Audio")]
        [SerializeField] private AudioSource audioSource;
        [SerializeField] private AudioClip damageSound;
        [SerializeField] private AudioClip healSound;
        [SerializeField] private AudioClip deathSound;
        
        // Events
        public System.Action<int, int> OnHealthChanged; // current, max
        public System.Action OnPlayerDeath;
        public System.Action OnPlayerRevived;
        
        // Private variables
        private bool isInvulnerable = false;
        private float lastDamageTime;
        private Color originalColor;
        private Material playerMaterial;
        private Coroutine damageFlashCoroutine;
        
        // Properties
        public int CurrentHealth => currentHealth;
        public int MaxHealth => maxHealth;
        public float HealthPercentage => (float)currentHealth / maxHealth;
        public bool IsAlive => currentHealth > 0;
        public bool IsInvulnerable => isInvulnerable || invulnerable;
        
        private void Awake()
        {
            // Initialize health
            currentHealth = maxHealth;
            
            // Set up components
            if (audioSource == null)
            {
                audioSource = GetComponent<AudioSource>();
                if (audioSource == null)
                {
                    audioSource = gameObject.AddComponent<AudioSource>();
                }
            }
            
            if (playerRenderer == null)
            {
                playerRenderer = GetComponentInChildren<Renderer>();
            }
            
            if (playerRenderer != null)
            {
                playerMaterial = playerRenderer.material;
                originalColor = playerMaterial.color;
            }
        }
        
        private void Start()
        {
            // Notify UI of initial health
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
        }
        
        private void Update()
        {
            HandleHealthRegeneration();
        }
        
        private void HandleHealthRegeneration()
        {
            if (enableHealthRegen && currentHealth < maxHealth && IsAlive)
            {
                // Check if enough time has passed since last damage
                if (Time.time - lastDamageTime >= regenDelay)
                {
                    float regenAmount = regenRate * Time.deltaTime;
                    Heal(Mathf.RoundToInt(regenAmount));
                }
            }
        }
        
        public void TakeDamage(int damage, Vector3 damageSource = default)
        {
            if (!IsAlive || IsInvulnerable || damage <= 0) return;
            
            // Apply damage
            currentHealth = Mathf.Max(0, currentHealth - damage);
            lastDamageTime = Time.time;
            
            Debug.Log($"PlayerHealth: Took {damage} damage. Health: {currentHealth}/{maxHealth}");
            
            // Trigger effects
            PlayDamageEffects();
            
            // Notify listeners
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
            
            // Check for death
            if (currentHealth <= 0)
            {
                Die();
            }
            else
            {
                // Start invulnerability period
                StartInvulnerability();
            }
        }
        
        public void Heal(int healAmount)
        {
            if (!IsAlive || healAmount <= 0) return;
            
            int oldHealth = currentHealth;
            currentHealth = Mathf.Min(maxHealth, currentHealth + healAmount);
            
            if (currentHealth > oldHealth)
            {
                Debug.Log($"PlayerHealth: Healed {currentHealth - oldHealth} health. Health: {currentHealth}/{maxHealth}");
                
                // Play heal sound
                if (audioSource != null && healSound != null)
                {
                    audioSource.PlayOneShot(healSound);
                }
                
                // Notify listeners
                OnHealthChanged?.Invoke(currentHealth, maxHealth);
            }
        }
        
        public void SetMaxHealth(int newMaxHealth)
        {
            maxHealth = Mathf.Max(1, newMaxHealth);
            currentHealth = Mathf.Min(currentHealth, maxHealth);
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
        }
        
        public void FullHeal()
        {
            Heal(maxHealth);
        }
        
        private void Die()
        {
            Debug.Log("PlayerHealth: Player died");
            
            // Play death sound
            if (audioSource != null && deathSound != null)
            {
                audioSource.PlayOneShot(deathSound);
            }
            
            // Disable player controls
            PlayerController playerController = GetComponent<PlayerController>();
            if (playerController != null)
            {
                playerController.SetMovementEnabled(false);
            }
            
            // Notify game manager
            if (GameManager.Instance != null)
            {
                GameManager.Instance.GameOver();
            }
            
            // Notify listeners
            OnPlayerDeath?.Invoke();
        }
        
        public void Revive(int reviveHealth = -1)
        {
            if (IsAlive) return;
            
            // Set health
            if (reviveHealth <= 0)
            {
                currentHealth = maxHealth;
            }
            else
            {
                currentHealth = Mathf.Min(reviveHealth, maxHealth);
            }
            
            Debug.Log($"PlayerHealth: Player revived with {currentHealth} health");
            
            // Re-enable player controls
            PlayerController playerController = GetComponent<PlayerController>();
            if (playerController != null)
            {
                playerController.SetMovementEnabled(true);
            }
            
            // Start invulnerability period
            StartInvulnerability();
            
            // Notify listeners
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
            OnPlayerRevived?.Invoke();
        }
        
        private void StartInvulnerability()
        {
            if (invulnerabilityDuration > 0f)
            {
                StartCoroutine(InvulnerabilityCoroutine());
            }
        }
        
        private System.Collections.IEnumerator InvulnerabilityCoroutine()
        {
            isInvulnerable = true;
            
            // Flash effect during invulnerability
            float flashInterval = 0.1f;
            float elapsed = 0f;
            
            while (elapsed < invulnerabilityDuration)
            {
                // Toggle visibility
                if (playerRenderer != null)
                {
                    playerRenderer.enabled = !playerRenderer.enabled;
                }
                
                yield return new WaitForSeconds(flashInterval);
                elapsed += flashInterval;
            }
            
            // Ensure renderer is enabled
            if (playerRenderer != null)
            {
                playerRenderer.enabled = true;
            }
            
            isInvulnerable = false;
        }
        
        private void PlayDamageEffects()
        {
            // Play damage sound
            if (audioSource != null && damageSound != null)
            {
                audioSource.PlayOneShot(damageSound);
            }
            
            // Flash damage color
            if (damageFlashCoroutine != null)
            {
                StopCoroutine(damageFlashCoroutine);
            }
            damageFlashCoroutine = StartCoroutine(DamageFlashCoroutine());
        }
        
        private System.Collections.IEnumerator DamageFlashCoroutine()
        {
            if (playerMaterial != null)
            {
                // Flash to damage color
                playerMaterial.color = damageColor;
                yield return new WaitForSeconds(damageFlashDuration);
                
                // Return to original color
                playerMaterial.color = originalColor;
            }
        }
        
        // Damage triggers
        private void OnTriggerEnter(Collider other)
        {
            // Check for damage sources
            if (other.CompareTag("Hazard"))
            {
                TakeDamage(10, other.transform.position);
            }
            else if (other.CompareTag("Enemy"))
            {
                TakeDamage(20, other.transform.position);
            }
        }
        
        private void OnCollisionEnter(Collision collision)
        {
            // Fall damage
            if (collision.gameObject.CompareTag("Ground"))
            {
                float fallSpeed = Mathf.Abs(collision.relativeVelocity.y);
                if (fallSpeed > 15f) // Threshold for fall damage
                {
                    int fallDamage = Mathf.RoundToInt((fallSpeed - 15f) * 2f);
                    TakeDamage(fallDamage);
                }
            }
        }
        
        // Public methods for external damage sources
        public void TakeDamageFromExplosion(int damage, Vector3 explosionCenter, float explosionRadius)
        {
            float distance = Vector3.Distance(transform.position, explosionCenter);
            float damageMultiplier = 1f - (distance / explosionRadius);
            damageMultiplier = Mathf.Clamp01(damageMultiplier);
            
            int actualDamage = Mathf.RoundToInt(damage * damageMultiplier);
            TakeDamage(actualDamage, explosionCenter);
        }
        
        public void TakeDamageOverTime(int damagePerSecond, float duration)
        {
            StartCoroutine(DamageOverTimeCoroutine(damagePerSecond, duration));
        }
        
        private System.Collections.IEnumerator DamageOverTimeCoroutine(int damagePerSecond, float duration)
        {
            float elapsed = 0f;
            float damageInterval = 1f;
            
            while (elapsed < duration && IsAlive)
            {
                TakeDamage(damagePerSecond);
                yield return new WaitForSeconds(damageInterval);
                elapsed += damageInterval;
            }
        }
        
        private void OnValidate()
        {
            maxHealth = Mathf.Max(1, maxHealth);
            currentHealth = Mathf.Clamp(currentHealth, 0, maxHealth);
            invulnerabilityDuration = Mathf.Max(0f, invulnerabilityDuration);
            regenRate = Mathf.Max(0f, regenRate);
            regenDelay = Mathf.Max(0f, regenDelay);
        }
    }
}
