using UnityEngine;
using CrystalQuest.Core;

namespace CrystalQuest.Utilities
{
    /// <summary>
    /// Utility script to help set up scenes with common game objects and components
    /// </summary>
    public class SceneSetup : MonoBehaviour
    {
        [Header("Scene Setup Options")]
        [SerializeField] private bool setupOnStart = true;
        [SerializeField] private bool createGameManager = true;
        [SerializeField] private bool createAudioManager = true;
        [SerializeField] private bool createUIManager = true;
        [SerializeField] private bool createObjectPooler = true;
        
        [Header("Lighting Setup")]
        [SerializeField] private bool setupLighting = true;
        [SerializeField] private Color ambientColor = new Color(0.2f, 0.2f, 0.3f);
        [SerializeField] private bool enableFog = true;
        [SerializeField] private Color fogColor = Color.gray;
        [SerializeField] private float fogDensity = 0.01f;
        
        [Header("Camera Setup")]
        [SerializeField] private bool setupMainCamera = true;
        [SerializeField] private Vector3 cameraPosition = new Vector3(0, 5, -10);
        [SerializeField] private Vector3 cameraRotation = new Vector3(15, 0, 0);
        
        private void Start()
        {
            if (setupOnStart)
            {
                SetupScene();
            }
        }
        
        [ContextMenu("Setup Scene")]
        public void SetupScene()
        {
            Debug.Log("SceneSetup: Setting up scene...");
            
            if (createGameManager) CreateGameManager();
            if (createAudioManager) CreateAudioManager();
            if (createUIManager) CreateUIManager();
            if (createObjectPooler) CreateObjectPooler();
            if (setupLighting) SetupSceneLighting();
            if (setupMainCamera) SetupCamera();
            
            Debug.Log("SceneSetup: Scene setup complete!");
        }
        
        private void CreateGameManager()
        {
            if (GameManager.Instance == null)
            {
                GameObject gameManagerGO = new GameObject("GameManager");
                gameManagerGO.AddComponent<GameManager>();
                Debug.Log("SceneSetup: Created GameManager");
            }
            else
            {
                Debug.Log("SceneSetup: GameManager already exists");
            }
        }
        
        private void CreateAudioManager()
        {
            if (AudioManager.Instance == null)
            {
                GameObject audioManagerGO = new GameObject("AudioManager");
                audioManagerGO.AddComponent<AudioManager>();
                Debug.Log("SceneSetup: Created AudioManager");
            }
            else
            {
                Debug.Log("SceneSetup: AudioManager already exists");
            }
        }
        
        private void CreateUIManager()
        {
            GameObject uiManagerGO = GameObject.Find("UIManager");
            if (uiManagerGO == null)
            {
                // Create Canvas for UI
                GameObject canvasGO = new GameObject("Canvas");
                Canvas canvas = canvasGO.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasGO.AddComponent<UnityEngine.UI.CanvasScaler>();
                canvasGO.AddComponent<UnityEngine.UI.GraphicRaycaster>();
                
                // Create EventSystem if it doesn't exist
                if (GameObject.Find("EventSystem") == null)
                {
                    GameObject eventSystemGO = new GameObject("EventSystem");
                    eventSystemGO.AddComponent<UnityEngine.EventSystems.EventSystem>();
                    eventSystemGO.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
                }
                
                // Create UIManager
                uiManagerGO = new GameObject("UIManager");
                uiManagerGO.AddComponent<CrystalQuest.UI.UIManager>();
                
                Debug.Log("SceneSetup: Created UI system");
            }
            else
            {
                Debug.Log("SceneSetup: UIManager already exists");
            }
        }
        
        private void CreateObjectPooler()
        {
            if (ObjectPooler.Instance == null)
            {
                GameObject objectPoolerGO = new GameObject("ObjectPooler");
                objectPoolerGO.AddComponent<ObjectPooler>();
                Debug.Log("SceneSetup: Created ObjectPooler");
            }
            else
            {
                Debug.Log("SceneSetup: ObjectPooler already exists");
            }
        }
        
        private void SetupSceneLighting()
        {
            // Set ambient lighting
            RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Flat;
            RenderSettings.ambientLight = ambientColor;
            
            // Setup fog
            RenderSettings.fog = enableFog;
            if (enableFog)
            {
                RenderSettings.fogColor = fogColor;
                RenderSettings.fogMode = FogMode.ExponentialSquared;
                RenderSettings.fogDensity = fogDensity;
            }
            
            // Create directional light if none exists
            Light[] lights = FindObjectsOfType<Light>();
            bool hasDirectionalLight = false;
            
            foreach (Light light in lights)
            {
                if (light.type == LightType.Directional)
                {
                    hasDirectionalLight = true;
                    break;
                }
            }
            
            if (!hasDirectionalLight)
            {
                GameObject lightGO = new GameObject("Directional Light");
                Light directionalLight = lightGO.AddComponent<Light>();
                directionalLight.type = LightType.Directional;
                directionalLight.color = Color.white;
                directionalLight.intensity = 1f;
                lightGO.transform.rotation = Quaternion.Euler(50f, -30f, 0f);
                
                Debug.Log("SceneSetup: Created directional light");
            }
            
            Debug.Log("SceneSetup: Lighting setup complete");
        }
        
        private void SetupCamera()
        {
            Camera mainCamera = Camera.main;
            
            if (mainCamera == null)
            {
                // Create main camera
                GameObject cameraGO = new GameObject("Main Camera");
                cameraGO.tag = "MainCamera";
                mainCamera = cameraGO.AddComponent<Camera>();
                cameraGO.AddComponent<AudioListener>();
                
                Debug.Log("SceneSetup: Created main camera");
            }
            
            // Position the camera
            mainCamera.transform.position = cameraPosition;
            mainCamera.transform.rotation = Quaternion.Euler(cameraRotation);
            
            // Set camera properties
            mainCamera.fieldOfView = 60f;
            mainCamera.nearClipPlane = 0.1f;
            mainCamera.farClipPlane = 1000f;
            
            Debug.Log("SceneSetup: Camera setup complete");
        }
        
        [ContextMenu("Create Test Environment")]
        public void CreateTestEnvironment()
        {
            // Create a simple test environment
            CreateGround();
            CreateTestCrystals();
            CreateTestPlayer();
            
            Debug.Log("SceneSetup: Test environment created");
        }
        
        private void CreateGround()
        {
            GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
            ground.name = "Ground";
            ground.transform.position = Vector3.zero;
            ground.transform.localScale = Vector3.one * 10f;
            
            // Add a simple material
            Renderer renderer = ground.GetComponent<Renderer>();
            if (renderer != null)
            {
                Material groundMaterial = new Material(Shader.Find("Standard"));
                groundMaterial.color = new Color(0.3f, 0.6f, 0.3f); // Green ground
                renderer.material = groundMaterial;
            }
        }
        
        private void CreateTestCrystals()
        {
            // Create some test crystal positions
            Vector3[] crystalPositions = {
                new Vector3(5, 1, 5),
                new Vector3(-5, 1, 5),
                new Vector3(5, 1, -5),
                new Vector3(-5, 1, -5),
                new Vector3(0, 1, 8)
            };
            
            foreach (Vector3 position in crystalPositions)
            {
                GameObject crystal = GameObject.CreatePrimitive(PrimitiveType.Cube);
                crystal.name = "Crystal";
                crystal.transform.position = position;
                crystal.transform.localScale = Vector3.one * 0.5f;
                
                // Add crystal component
                crystal.AddComponent<CrystalQuest.Player.Crystal>();
                
                // Make it a trigger
                Collider collider = crystal.GetComponent<Collider>();
                if (collider != null)
                {
                    collider.isTrigger = true;
                }
                
                // Add a simple glowing material
                Renderer renderer = crystal.GetComponent<Renderer>();
                if (renderer != null)
                {
                    Material crystalMaterial = new Material(Shader.Find("Standard"));
                    crystalMaterial.color = Color.cyan;
                    crystalMaterial.EnableKeyword("_EMISSION");
                    crystalMaterial.SetColor("_EmissionColor", Color.cyan * 0.5f);
                    renderer.material = crystalMaterial;
                }
            }
        }
        
        private void CreateTestPlayer()
        {
            GameObject player = new GameObject("Player");
            player.tag = "Player";
            
            // Add character controller
            CharacterController controller = player.AddComponent<CharacterController>();
            controller.height = 2f;
            controller.radius = 0.5f;
            controller.center = new Vector3(0, 1, 0);
            
            // Add player controller
            player.AddComponent<CrystalQuest.Player.PlayerController>();
            
            // Position player
            player.transform.position = new Vector3(0, 1, 0);
            
            // Create a simple visual representation
            GameObject playerVisual = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            playerVisual.name = "PlayerVisual";
            playerVisual.transform.SetParent(player.transform);
            playerVisual.transform.localPosition = new Vector3(0, 1, 0);
            
            // Remove the collider from visual (CharacterController handles collision)
            Collider visualCollider = playerVisual.GetComponent<Collider>();
            if (visualCollider != null)
            {
                DestroyImmediate(visualCollider);
            }
            
            Debug.Log("SceneSetup: Test player created");
        }
    }
}
