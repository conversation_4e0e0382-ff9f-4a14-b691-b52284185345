using UnityEngine;
using CrystalQuest.Core;

namespace CrystalQuest.Player
{
    /// <summary>
    /// Advanced camera controller with smooth following, look-at, and various camera modes
    /// </summary>
    public class CameraController : MonoBehaviour
    {
        [Header("Target Settings")]
        [SerializeField] private Transform target;
        [SerializeField] private Vector3 offset = new Vector3(0, 5, -10);
        [SerializeField] private bool lookAtTarget = true;
        [SerializeField] private Vector3 lookAtOffset = Vector3.zero;
        
        [Header("Camera Modes")]
        [SerializeField] private CameraMode currentMode = CameraMode.ThirdPerson;
        [SerializeField] private bool allowModeSwitch = true;
        [SerializeField] private KeyCode switchModeKey = KeyCode.C;
        
        [Header("Third Person Settings")]
        [SerializeField] private float followSpeed = 5f;
        [SerializeField] private float rotationSpeed = 3f;
        [SerializeField] private float minDistance = 2f;
        [SerializeField] private float maxDistance = 15f;
        [SerializeField] private float zoomSpeed = 2f;
        
        [Header("First Person Settings")]
        [SerializeField] private Vector3 firstPersonOffset = new Vector3(0, 1.8f, 0);
        [SerializeField] private float mouseSensitivity = 2f;
        [SerializeField] private float maxLookAngle = 80f;
        
        [Header("Collision Detection")]
        [SerializeField] private bool enableCollisionDetection = true;
        [SerializeField] private LayerMask collisionLayers = -1;
        [SerializeField] private float collisionRadius = 0.3f;
        
        [Header("Camera Shake")]
        [SerializeField] private bool enableCameraShake = true;
        [SerializeField] private float shakeDecay = 5f;
        
        [Header("Smooth Transitions")]
        [SerializeField] private bool smoothTransitions = true;
        [SerializeField] private float transitionSpeed = 2f;
        
        // Private variables
        private Camera cameraComponent;
        private Vector3 currentVelocity;
        private Vector3 targetPosition;
        private Quaternion targetRotation;
        private float currentDistance;
        private float mouseX, mouseY;
        private float xRotation = 0f;
        
        // Camera shake variables
        private Vector3 shakeOffset = Vector3.zero;
        private float shakeIntensity = 0f;
        
        // Mode transition variables
        private Vector3 transitionStartPosition;
        private Quaternion transitionStartRotation;
        private float transitionProgress = 1f;
        
        // Properties
        public CameraMode CurrentMode => currentMode;
        public Transform Target => target;
        public Camera Camera => cameraComponent;
        
        private void Awake()
        {
            cameraComponent = GetComponent<Camera>();
            if (cameraComponent == null)
            {
                cameraComponent = gameObject.AddComponent<Camera>();
            }
            
            currentDistance = offset.magnitude;
            
            // Find target if not assigned
            if (target == null)
            {
                GameObject player = GameObject.FindGameObjectWithTag("Player");
                if (player != null)
                {
                    target = player.transform;
                }
            }
        }
        
        private void Start()
        {
            if (target != null)
            {
                // Initialize position
                UpdateCameraPosition();
            }
        }
        
        private void Update()
        {
            if (target == null) return;
            
            HandleInput();
            UpdateCameraPosition();
            UpdateCameraRotation();
            ApplyCameraShake();
        }
        
        private void LateUpdate()
        {
            if (target == null) return;
            
            // Apply final position and rotation
            if (smoothTransitions && transitionProgress < 1f)
            {
                HandleModeTransition();
            }
            else
            {
                transform.position = targetPosition + shakeOffset;
                transform.rotation = targetRotation;
            }
        }
        
        private void HandleInput()
        {
            // Only handle input if game is playing
            if (GameManager.Instance != null && GameManager.Instance.CurrentState != GameState.Playing)
                return;
            
            // Mode switching
            if (allowModeSwitch && Input.GetKeyDown(switchModeKey))
            {
                SwitchCameraMode();
            }
            
            // Mouse input for camera control
            if (currentMode == CameraMode.ThirdPerson)
            {
                HandleThirdPersonInput();
            }
            else if (currentMode == CameraMode.FirstPerson)
            {
                HandleFirstPersonInput();
            }
        }
        
        private void HandleThirdPersonInput()
        {
            // Mouse look
            if (Input.GetMouseButton(1)) // Right mouse button
            {
                mouseX += Input.GetAxis("Mouse X") * rotationSpeed;
                mouseY -= Input.GetAxis("Mouse Y") * rotationSpeed;
                mouseY = Mathf.Clamp(mouseY, -maxLookAngle, maxLookAngle);
            }
            
            // Zoom with scroll wheel
            float scroll = Input.GetAxis("Mouse ScrollWheel");
            if (scroll != 0f)
            {
                currentDistance -= scroll * zoomSpeed;
                currentDistance = Mathf.Clamp(currentDistance, minDistance, maxDistance);
            }
        }
        
        private void HandleFirstPersonInput()
        {
            mouseX += Input.GetAxis("Mouse X") * mouseSensitivity;
            mouseY -= Input.GetAxis("Mouse Y") * mouseSensitivity;
            mouseY = Mathf.Clamp(mouseY, -maxLookAngle, maxLookAngle);
        }
        
        private void UpdateCameraPosition()
        {
            switch (currentMode)
            {
                case CameraMode.ThirdPerson:
                    UpdateThirdPersonPosition();
                    break;
                case CameraMode.FirstPerson:
                    UpdateFirstPersonPosition();
                    break;
                case CameraMode.Fixed:
                    UpdateFixedPosition();
                    break;
                case CameraMode.Orbital:
                    UpdateOrbitalPosition();
                    break;
            }
        }
        
        private void UpdateThirdPersonPosition()
        {
            // Calculate desired position
            Vector3 desiredPosition = target.position + offset.normalized * currentDistance;
            
            // Apply rotation based on mouse input
            Quaternion rotation = Quaternion.Euler(mouseY, mouseX, 0);
            desiredPosition = target.position + rotation * (Vector3.back * currentDistance);
            
            // Check for collisions
            if (enableCollisionDetection)
            {
                desiredPosition = HandleCameraCollision(target.position, desiredPosition);
            }
            
            // Smooth movement
            if (smoothTransitions)
            {
                targetPosition = Vector3.SmoothDamp(transform.position, desiredPosition, ref currentVelocity, 1f / followSpeed);
            }
            else
            {
                targetPosition = desiredPosition;
            }
        }
        
        private void UpdateFirstPersonPosition()
        {
            targetPosition = target.position + firstPersonOffset;
        }
        
        private void UpdateFixedPosition()
        {
            // Keep current position (fixed camera)
            targetPosition = transform.position;
        }
        
        private void UpdateOrbitalPosition()
        {
            // Orbit around target
            float orbitSpeed = 20f;
            mouseX += orbitSpeed * Time.deltaTime;
            
            Quaternion rotation = Quaternion.Euler(mouseY, mouseX, 0);
            targetPosition = target.position + rotation * (Vector3.back * currentDistance);
        }
        
        private void UpdateCameraRotation()
        {
            switch (currentMode)
            {
                case CameraMode.ThirdPerson:
                    if (lookAtTarget)
                    {
                        Vector3 lookAtPoint = target.position + lookAtOffset;
                        targetRotation = Quaternion.LookRotation(lookAtPoint - targetPosition);
                    }
                    else
                    {
                        targetRotation = Quaternion.Euler(mouseY, mouseX, 0);
                    }
                    break;
                    
                case CameraMode.FirstPerson:
                    targetRotation = Quaternion.Euler(mouseY, mouseX, 0);
                    break;
                    
                case CameraMode.Fixed:
                    if (lookAtTarget)
                    {
                        Vector3 lookAtPoint = target.position + lookAtOffset;
                        targetRotation = Quaternion.LookRotation(lookAtPoint - transform.position);
                    }
                    break;
                    
                case CameraMode.Orbital:
                    Vector3 orbitalLookAt = target.position + lookAtOffset;
                    targetRotation = Quaternion.LookRotation(orbitalLookAt - targetPosition);
                    break;
            }
        }
        
        private Vector3 HandleCameraCollision(Vector3 targetPos, Vector3 desiredPos)
        {
            Vector3 direction = desiredPos - targetPos;
            float distance = direction.magnitude;
            
            RaycastHit hit;
            if (Physics.SphereCast(targetPos, collisionRadius, direction.normalized, out hit, distance, collisionLayers))
            {
                // Move camera closer to avoid collision
                return targetPos + direction.normalized * (hit.distance - collisionRadius);
            }
            
            return desiredPos;
        }
        
        private void SwitchCameraMode()
        {
            // Start transition
            transitionStartPosition = transform.position;
            transitionStartRotation = transform.rotation;
            transitionProgress = 0f;
            
            // Switch to next mode
            int nextMode = ((int)currentMode + 1) % System.Enum.GetValues(typeof(CameraMode)).Length;
            currentMode = (CameraMode)nextMode;
            
            Debug.Log($"CameraController: Switched to {currentMode} mode");
        }
        
        private void HandleModeTransition()
        {
            transitionProgress += transitionSpeed * Time.deltaTime;
            transitionProgress = Mathf.Clamp01(transitionProgress);
            
            // Interpolate position and rotation
            transform.position = Vector3.Lerp(transitionStartPosition, targetPosition, transitionProgress) + shakeOffset;
            transform.rotation = Quaternion.Lerp(transitionStartRotation, targetRotation, transitionProgress);
        }
        
        public void ShakeCamera(float intensity, float duration)
        {
            if (!enableCameraShake) return;
            
            shakeIntensity = Mathf.Max(shakeIntensity, intensity);
            StartCoroutine(CameraShakeCoroutine(duration));
        }
        
        private System.Collections.IEnumerator CameraShakeCoroutine(float duration)
        {
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                yield return null;
            }
        }
        
        private void ApplyCameraShake()
        {
            if (shakeIntensity > 0f)
            {
                // Generate random shake offset
                shakeOffset = Random.insideUnitSphere * shakeIntensity;
                
                // Decay shake intensity
                shakeIntensity -= shakeDecay * Time.deltaTime;
                shakeIntensity = Mathf.Max(0f, shakeIntensity);
            }
            else
            {
                shakeOffset = Vector3.zero;
            }
        }
        
        public void SetTarget(Transform newTarget)
        {
            target = newTarget;
        }
        
        public void SetCameraMode(CameraMode mode)
        {
            if (mode != currentMode)
            {
                transitionStartPosition = transform.position;
                transitionStartRotation = transform.rotation;
                transitionProgress = 0f;
                currentMode = mode;
            }
        }
        
        public void SetOffset(Vector3 newOffset)
        {
            offset = newOffset;
            currentDistance = offset.magnitude;
        }
        
        private void OnDrawGizmosSelected()
        {
            if (target != null)
            {
                // Draw target connection
                Gizmos.color = Color.yellow;
                Gizmos.DrawLine(transform.position, target.position);
                
                // Draw look at point
                if (lookAtTarget)
                {
                    Vector3 lookAtPoint = target.position + lookAtOffset;
                    Gizmos.color = Color.red;
                    Gizmos.DrawWireSphere(lookAtPoint, 0.5f);
                }
                
                // Draw collision sphere
                if (enableCollisionDetection)
                {
                    Gizmos.color = Color.blue;
                    Gizmos.DrawWireSphere(transform.position, collisionRadius);
                }
            }
        }
    }
    
    public enum CameraMode
    {
        ThirdPerson,
        FirstPerson,
        Fixed,
        Orbital
    }
}
