using UnityEngine;
using System.Collections.Generic;
using CrystalQuest.Core;

namespace CrystalQuest.Gameplay
{
    /// <summary>
    /// Manages game objectives, quests, and mission completion
    /// </summary>
    public class ObjectiveManager : MonoBehaviour
    {
        [Header("Objective Settings")]
        [SerializeField] private List<Objective> currentObjectives = new List<Objective>();
        [SerializeField] private bool showObjectiveNotifications = true;
        [SerializeField] private float notificationDuration = 3f;
        
        // Singleton instance
        public static ObjectiveManager Instance { get; private set; }
        
        // Events
        public System.Action<Objective> OnObjectiveAdded;
        public System.Action<Objective> OnObjectiveCompleted;
        public System.Action<Objective> OnObjectiveFailed;
        public System.Action<Objective> OnObjectiveUpdated;
        public System.Action OnAllObjectivesCompleted;
        
        // Properties
        public List<Objective> CurrentObjectives => new List<Objective>(currentObjectives);
        public int CompletedObjectivesCount => currentObjectives.FindAll(o => o.IsCompleted).Count;
        public int TotalObjectivesCount => currentObjectives.Count;
        public bool AllObjectivesCompleted => currentObjectives.Count > 0 && currentObjectives.TrueForAll(o => o.IsCompleted);
        
        private void Awake()
        {
            // Implement singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeObjectiveManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeObjectiveManager()
        {
            Debug.Log("ObjectiveManager: Initialized successfully");
        }
        
        private void Start()
        {
            // Subscribe to game events
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnCrystalsChanged += OnCrystalsChanged;
                GameManager.Instance.OnScoreChanged += OnScoreChanged;
            }
            
            // Initialize default objectives
            SetupDefaultObjectives();
        }
        
        private void SetupDefaultObjectives()
        {
            // Create default objectives for the level
            AddObjective(new Objective
            {
                id = "collect_crystals",
                title = "Collect All Crystals",
                description = "Find and collect all crystals in the level",
                type = ObjectiveType.Collection,
                targetValue = GameManager.Instance?.TotalCrystals ?? 5,
                currentValue = 0,
                isRequired = true
            });
            
            AddObjective(new Objective
            {
                id = "survive",
                title = "Stay Alive",
                description = "Complete the level without dying",
                type = ObjectiveType.Survival,
                targetValue = 1,
                currentValue = 1,
                isRequired = true
            });
            
            AddObjective(new Objective
            {
                id = "score_bonus",
                title = "Score Master",
                description = "Achieve a score of 1000 points",
                type = ObjectiveType.Score,
                targetValue = 1000,
                currentValue = 0,
                isRequired = false
            });
        }
        
        public void AddObjective(Objective objective)
        {
            if (objective == null || string.IsNullOrEmpty(objective.id)) return;
            
            // Check if objective already exists
            if (currentObjectives.Exists(o => o.id == objective.id))
            {
                Debug.LogWarning($"ObjectiveManager: Objective with ID '{objective.id}' already exists");
                return;
            }
            
            currentObjectives.Add(objective);
            OnObjectiveAdded?.Invoke(objective);
            
            Debug.Log($"ObjectiveManager: Added objective '{objective.title}'");
        }
        
        public void RemoveObjective(string objectiveId)
        {
            Objective objective = currentObjectives.Find(o => o.id == objectiveId);
            if (objective != null)
            {
                currentObjectives.Remove(objective);
                Debug.Log($"ObjectiveManager: Removed objective '{objective.title}'");
            }
        }
        
        public void UpdateObjectiveProgress(string objectiveId, int newValue)
        {
            Objective objective = GetObjective(objectiveId);
            if (objective != null)
            {
                int oldValue = objective.currentValue;
                objective.currentValue = newValue;
                
                // Check for completion
                if (!objective.IsCompleted && objective.currentValue >= objective.targetValue)
                {
                    CompleteObjective(objectiveId);
                }
                else if (oldValue != newValue)
                {
                    OnObjectiveUpdated?.Invoke(objective);
                }
            }
        }
        
        public void IncrementObjectiveProgress(string objectiveId, int increment = 1)
        {
            Objective objective = GetObjective(objectiveId);
            if (objective != null)
            {
                UpdateObjectiveProgress(objectiveId, objective.currentValue + increment);
            }
        }
        
        public void CompleteObjective(string objectiveId)
        {
            Objective objective = GetObjective(objectiveId);
            if (objective != null && !objective.IsCompleted)
            {
                objective.isCompleted = true;
                objective.completionTime = Time.time;
                
                OnObjectiveCompleted?.Invoke(objective);
                
                // Award score for completing objective
                if (ScoreManager.Instance != null)
                {
                    int scoreReward = objective.isRequired ? 200 : 100;
                    ScoreManager.Instance.AddCustomScore(scoreReward, $"Objective: {objective.title}");
                }
                
                Debug.Log($"ObjectiveManager: Completed objective '{objective.title}'");
                
                // Check if all objectives are completed
                if (AllObjectivesCompleted)
                {
                    OnAllObjectivesCompleted?.Invoke();
                    
                    // Notify game manager
                    if (GameManager.Instance != null)
                    {
                        // Level completion logic would go here
                    }
                }
            }
        }
        
        public void FailObjective(string objectiveId, string reason = "")
        {
            Objective objective = GetObjective(objectiveId);
            if (objective != null && !objective.IsCompleted && !objective.isFailed)
            {
                objective.isFailed = true;
                objective.failureReason = reason;
                
                OnObjectiveFailed?.Invoke(objective);
                
                Debug.Log($"ObjectiveManager: Failed objective '{objective.title}': {reason}");
                
                // Check if this was a required objective
                if (objective.isRequired && GameManager.Instance != null)
                {
                    GameManager.Instance.GameOver();
                }
            }
        }
        
        public Objective GetObjective(string objectiveId)
        {
            return currentObjectives.Find(o => o.id == objectiveId);
        }
        
        public List<Objective> GetObjectivesByType(ObjectiveType type)
        {
            return currentObjectives.FindAll(o => o.type == type);
        }
        
        public List<Objective> GetRequiredObjectives()
        {
            return currentObjectives.FindAll(o => o.isRequired);
        }
        
        public List<Objective> GetOptionalObjectives()
        {
            return currentObjectives.FindAll(o => !o.isRequired);
        }
        
        public List<Objective> GetCompletedObjectives()
        {
            return currentObjectives.FindAll(o => o.IsCompleted);
        }
        
        public List<Objective> GetFailedObjectives()
        {
            return currentObjectives.FindAll(o => o.isFailed);
        }
        
        public float GetCompletionPercentage()
        {
            if (currentObjectives.Count == 0) return 100f;
            
            int completedCount = GetCompletedObjectives().Count;
            return (float)completedCount / currentObjectives.Count * 100f;
        }
        
        public void ClearAllObjectives()
        {
            currentObjectives.Clear();
            Debug.Log("ObjectiveManager: Cleared all objectives");
        }
        
        public void ResetObjectives()
        {
            foreach (Objective objective in currentObjectives)
            {
                objective.currentValue = 0;
                objective.isCompleted = false;
                objective.isFailed = false;
                objective.completionTime = 0f;
                objective.failureReason = "";
            }
            
            Debug.Log("ObjectiveManager: Reset all objectives");
        }
        
        // Event handlers
        private void OnCrystalsChanged(int collected, int total)
        {
            UpdateObjectiveProgress("collect_crystals", collected);
        }
        
        private void OnScoreChanged(int newScore)
        {
            UpdateObjectiveProgress("score_bonus", newScore);
        }
        
        // Player death handler
        public void OnPlayerDeath()
        {
            FailObjective("survive", "Player died");
        }
        
        private void OnDestroy()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnCrystalsChanged -= OnCrystalsChanged;
                GameManager.Instance.OnScoreChanged -= OnScoreChanged;
            }
        }
    }
    
    [System.Serializable]
    public class Objective
    {
        [Header("Basic Info")]
        public string id;
        public string title;
        [TextArea(2, 4)]
        public string description;
        public ObjectiveType type;
        
        [Header("Progress")]
        public int currentValue;
        public int targetValue;
        public bool isCompleted;
        public bool isFailed;
        
        [Header("Settings")]
        public bool isRequired = true;
        public bool isHidden = false;
        public int priority = 0;
        
        [Header("Rewards")]
        public int scoreReward = 100;
        public string[] unlockItems;
        
        [Header("Runtime Data")]
        public float completionTime;
        public string failureReason;
        
        // Properties
        public bool IsCompleted => isCompleted;
        public bool IsFailed => isFailed;
        public bool IsActive => !isCompleted && !isFailed;
        public float Progress => targetValue > 0 ? (float)currentValue / targetValue : 0f;
        public int RemainingValue => Mathf.Max(0, targetValue - currentValue);
        
        public string GetProgressText()
        {
            switch (type)
            {
                case ObjectiveType.Collection:
                    return $"{currentValue}/{targetValue}";
                case ObjectiveType.Score:
                    return $"{currentValue:N0}/{targetValue:N0}";
                case ObjectiveType.Time:
                    return $"{currentValue}s/{targetValue}s";
                case ObjectiveType.Survival:
                    return isCompleted ? "Completed" : "In Progress";
                default:
                    return $"{currentValue}/{targetValue}";
            }
        }
        
        public string GetStatusText()
        {
            if (isCompleted) return "Completed";
            if (isFailed) return "Failed";
            return "In Progress";
        }
    }
    
    public enum ObjectiveType
    {
        Collection,     // Collect X items
        Score,          // Achieve X score
        Time,           // Complete within X time
        Survival,       // Stay alive
        Exploration,    // Visit X locations
        Combat,         // Defeat X enemies
        Custom          // Custom objective
    }
}
