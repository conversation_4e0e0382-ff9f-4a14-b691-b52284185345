using UnityEngine;
using System.Collections.Generic;
using CrystalQuest.Utilities;

namespace CrystalQuest.Effects
{
    /// <summary>
    /// Manages particle effects throughout the game with pooling and optimization
    /// </summary>
    public class ParticleEffectManager : MonoBehaviour
    {
        [Header("Effect Prefabs")]
        [SerializeField] private GameObject crystalCollectEffect;
        [SerializeField] private GameObject playerJumpEffect;
        [SerializeField] private GameObject playerLandEffect;
        [SerializeField] private GameObject explosionEffect;
        [SerializeField] private GameObject healEffect;
        [SerializeField] private GameObject damageEffect;
        [SerializeField] private GameObject levelCompleteEffect;
        
        [Header("Pool Settings")]
        [SerializeField] private int poolSize = 20;
        [SerializeField] private bool expandPool = true;
        
        [Header("Performance Settings")]
        [SerializeField] private int maxActiveEffects = 50;
        [SerializeField] private float effectCullDistance = 100f;
        [SerializeField] private bool enableLOD = true;
        
        // Singleton instance
        public static ParticleEffectManager Instance { get; private set; }
        
        // Effect pools
        private Dictionary<EffectType, Queue<GameObject>> effectPools = new Dictionary<EffectType, Queue<GameObject>>();
        private List<ActiveEffect> activeEffects = new List<ActiveEffect>();
        private Transform effectParent;
        private Camera playerCamera;
        
        private void Awake()
        {
            // Implement singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeEffectManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeEffectManager()
        {
            // Create parent object for organization
            GameObject parentGO = new GameObject("ParticleEffects");
            parentGO.transform.SetParent(transform);
            effectParent = parentGO.transform;
            
            // Initialize effect pools
            InitializeEffectPools();
            
            // Find player camera
            playerCamera = Camera.main;
            
            Debug.Log("ParticleEffectManager: Initialized successfully");
        }
        
        private void InitializeEffectPools()
        {
            // Initialize pools for each effect type
            CreateEffectPool(EffectType.CrystalCollect, crystalCollectEffect);
            CreateEffectPool(EffectType.PlayerJump, playerJumpEffect);
            CreateEffectPool(EffectType.PlayerLand, playerLandEffect);
            CreateEffectPool(EffectType.Explosion, explosionEffect);
            CreateEffectPool(EffectType.Heal, healEffect);
            CreateEffectPool(EffectType.Damage, damageEffect);
            CreateEffectPool(EffectType.LevelComplete, levelCompleteEffect);
        }
        
        private void CreateEffectPool(EffectType effectType, GameObject prefab)
        {
            if (prefab == null) return;
            
            Queue<GameObject> pool = new Queue<GameObject>();
            
            // Create parent for this effect type
            GameObject typeParent = new GameObject($"{effectType}Effects");
            typeParent.transform.SetParent(effectParent);
            
            // Pre-instantiate effects
            for (int i = 0; i < poolSize; i++)
            {
                GameObject effect = CreatePooledEffect(prefab, typeParent.transform);
                pool.Enqueue(effect);
            }
            
            effectPools[effectType] = pool;
        }
        
        private GameObject CreatePooledEffect(GameObject prefab, Transform parent)
        {
            GameObject effect = Instantiate(prefab, parent);
            effect.SetActive(false);
            
            // Add PooledObject component if it doesn't exist
            if (effect.GetComponent<PooledObject>() == null)
            {
                effect.AddComponent<PooledObject>();
            }
            
            // Ensure particle systems stop on disable
            ParticleSystem[] particleSystems = effect.GetComponentsInChildren<ParticleSystem>();
            foreach (ParticleSystem ps in particleSystems)
            {
                var main = ps.main;
                main.stopAction = ParticleSystemStopAction.Disable;
            }
            
            return effect;
        }
        
        private void Update()
        {
            UpdateActiveEffects();
            CullDistantEffects();
        }
        
        private void UpdateActiveEffects()
        {
            for (int i = activeEffects.Count - 1; i >= 0; i--)
            {
                ActiveEffect activeEffect = activeEffects[i];
                
                // Check if effect is still playing
                if (!IsEffectPlaying(activeEffect.gameObject))
                {
                    ReturnEffectToPool(activeEffect);
                    activeEffects.RemoveAt(i);
                }
                else
                {
                    // Update LOD if enabled
                    if (enableLOD && playerCamera != null)
                    {
                        UpdateEffectLOD(activeEffect);
                    }
                }
            }
        }
        
        private void CullDistantEffects()
        {
            if (playerCamera == null) return;
            
            Vector3 cameraPosition = playerCamera.transform.position;
            
            for (int i = activeEffects.Count - 1; i >= 0; i--)
            {
                ActiveEffect activeEffect = activeEffects[i];
                float distance = Vector3.Distance(cameraPosition, activeEffect.gameObject.transform.position);
                
                if (distance > effectCullDistance)
                {
                    ReturnEffectToPool(activeEffect);
                    activeEffects.RemoveAt(i);
                }
            }
        }
        
        private void UpdateEffectLOD(ActiveEffect activeEffect)
        {
            if (playerCamera == null) return;
            
            float distance = Vector3.Distance(playerCamera.transform.position, activeEffect.gameObject.transform.position);
            float lodLevel = Mathf.Clamp01(distance / (effectCullDistance * 0.5f));
            
            // Adjust particle count based on distance
            ParticleSystem[] particleSystems = activeEffect.gameObject.GetComponentsInChildren<ParticleSystem>();
            foreach (ParticleSystem ps in particleSystems)
            {
                var emission = ps.emission;
                float originalRate = activeEffect.originalEmissionRate;
                emission.rateOverTime = originalRate * (1f - lodLevel * 0.7f);
            }
        }
        
        public GameObject PlayEffect(EffectType effectType, Vector3 position, Quaternion rotation = default, Transform parent = null)
        {
            if (!effectPools.ContainsKey(effectType))
            {
                Debug.LogWarning($"ParticleEffectManager: No pool found for effect type {effectType}");
                return null;
            }
            
            // Check active effect limit
            if (activeEffects.Count >= maxActiveEffects)
            {
                // Remove oldest effect
                ReturnEffectToPool(activeEffects[0]);
                activeEffects.RemoveAt(0);
            }
            
            GameObject effect = GetEffectFromPool(effectType);
            if (effect == null) return null;
            
            // Set position and rotation
            effect.transform.position = position;
            effect.transform.rotation = rotation == default ? Quaternion.identity : rotation;
            
            // Set parent
            if (parent != null)
            {
                effect.transform.SetParent(parent);
            }
            else
            {
                effect.transform.SetParent(effectParent);
            }
            
            // Activate effect
            effect.SetActive(true);
            
            // Play all particle systems
            ParticleSystem[] particleSystems = effect.GetComponentsInChildren<ParticleSystem>();
            float originalEmissionRate = 0f;
            
            foreach (ParticleSystem ps in particleSystems)
            {
                ps.Play();
                if (originalEmissionRate == 0f)
                {
                    originalEmissionRate = ps.emission.rateOverTime.constant;
                }
            }
            
            // Add to active effects
            ActiveEffect activeEffect = new ActiveEffect
            {
                gameObject = effect,
                effectType = effectType,
                startTime = Time.time,
                originalEmissionRate = originalEmissionRate
            };
            
            activeEffects.Add(activeEffect);
            
            return effect;
        }
        
        private GameObject GetEffectFromPool(EffectType effectType)
        {
            Queue<GameObject> pool = effectPools[effectType];
            
            // Try to get an inactive effect from the pool
            int attempts = 0;
            while (attempts < pool.Count)
            {
                GameObject effect = pool.Dequeue();
                pool.Enqueue(effect); // Put it back in the queue
                
                if (!effect.activeInHierarchy)
                {
                    return effect;
                }
                
                attempts++;
            }
            
            // If no inactive effect found and pool can expand, create a new one
            if (expandPool)
            {
                GameObject prefab = GetEffectPrefab(effectType);
                if (prefab != null)
                {
                    Transform typeParent = effectParent.Find($"{effectType}Effects");
                    GameObject newEffect = CreatePooledEffect(prefab, typeParent);
                    pool.Enqueue(newEffect);
                    return newEffect;
                }
            }
            
            return null;
        }
        
        private GameObject GetEffectPrefab(EffectType effectType)
        {
            switch (effectType)
            {
                case EffectType.CrystalCollect: return crystalCollectEffect;
                case EffectType.PlayerJump: return playerJumpEffect;
                case EffectType.PlayerLand: return playerLandEffect;
                case EffectType.Explosion: return explosionEffect;
                case EffectType.Heal: return healEffect;
                case EffectType.Damage: return damageEffect;
                case EffectType.LevelComplete: return levelCompleteEffect;
                default: return null;
            }
        }
        
        private bool IsEffectPlaying(GameObject effect)
        {
            ParticleSystem[] particleSystems = effect.GetComponentsInChildren<ParticleSystem>();
            
            foreach (ParticleSystem ps in particleSystems)
            {
                if (ps.isPlaying)
                {
                    return true;
                }
            }
            
            return false;
        }
        
        private void ReturnEffectToPool(ActiveEffect activeEffect)
        {
            if (activeEffect.gameObject != null)
            {
                activeEffect.gameObject.SetActive(false);
                activeEffect.gameObject.transform.SetParent(effectParent.Find($"{activeEffect.effectType}Effects"));
            }
        }
        
        // Convenience methods for common effects
        public void PlayCrystalCollectEffect(Vector3 position)
        {
            PlayEffect(EffectType.CrystalCollect, position);
        }
        
        public void PlayPlayerJumpEffect(Vector3 position)
        {
            PlayEffect(EffectType.PlayerJump, position);
        }
        
        public void PlayPlayerLandEffect(Vector3 position)
        {
            PlayEffect(EffectType.PlayerLand, position);
        }
        
        public void PlayExplosionEffect(Vector3 position, float scale = 1f)
        {
            GameObject effect = PlayEffect(EffectType.Explosion, position);
            if (effect != null && scale != 1f)
            {
                effect.transform.localScale = Vector3.one * scale;
            }
        }
        
        public void PlayHealEffect(Vector3 position, Transform parent = null)
        {
            PlayEffect(EffectType.Heal, position, Quaternion.identity, parent);
        }
        
        public void PlayDamageEffect(Vector3 position)
        {
            PlayEffect(EffectType.Damage, position);
        }
        
        public void PlayLevelCompleteEffect(Vector3 position)
        {
            PlayEffect(EffectType.LevelComplete, position);
        }
        
        public void StopAllEffects()
        {
            foreach (ActiveEffect activeEffect in activeEffects)
            {
                if (activeEffect.gameObject != null)
                {
                    ParticleSystem[] particleSystems = activeEffect.gameObject.GetComponentsInChildren<ParticleSystem>();
                    foreach (ParticleSystem ps in particleSystems)
                    {
                        ps.Stop();
                    }
                }
            }
        }
        
        public void ClearAllEffects()
        {
            StopAllEffects();
            
            foreach (ActiveEffect activeEffect in activeEffects)
            {
                ReturnEffectToPool(activeEffect);
            }
            
            activeEffects.Clear();
        }
        
        public int GetActiveEffectCount()
        {
            return activeEffects.Count;
        }
        
        public int GetActiveEffectCount(EffectType effectType)
        {
            return activeEffects.FindAll(e => e.effectType == effectType).Count;
        }
    }
    
    [System.Serializable]
    public class ActiveEffect
    {
        public GameObject gameObject;
        public EffectType effectType;
        public float startTime;
        public float originalEmissionRate;
    }
    
    public enum EffectType
    {
        CrystalCollect,
        PlayerJump,
        PlayerLand,
        Explosion,
        Heal,
        Damage,
        LevelComplete
    }
}
