using UnityEngine;
using CrystalQuest.Core;
using CrystalQuest.UI;
using CrystalQuest.Utilities;
using CrystalQuest.Testing;

namespace CrystalQuest.Core
{
    /// <summary>
    /// Initializes the game and ensures all core systems are properly set up
    /// </summary>
    public class GameInitializer : MonoBehaviour
    {
        [Header("Initialization Settings")]
        [SerializeField] private bool initializeOnAwake = true;
        [SerializeField] private bool enablePerformanceMonitoring = true;
        [SerializeField] private bool enableTesting = false;
        [SerializeField] private bool enableDebugMode = false;
        
        [Header("Core Systems")]
        [SerializeField] private GameObject gameManagerPrefab;
        [SerializeField] private GameObject audioManagerPrefab;
        [SerializeField] private GameObject uiManagerPrefab;
        [SerializeField] private GameObject saveSystemPrefab;
        [SerializeField] private GameObject inputManagerPrefab;
        
        [Header("Optional Systems")]
        [SerializeField] private GameObject objectPoolerPrefab;
        [SerializeField] private GameObject performanceMonitorPrefab;
        [SerializeField] private GameObject gameTesterPrefab;
        
        private void Awake()
        {
            if (initializeOnAwake)
            {
                InitializeGame();
            }
        }
        
        [ContextMenu("Initialize Game")]
        public void InitializeGame()
        {
            Debug.Log("GameInitializer: Starting game initialization...");
            
            // Initialize core systems
            InitializeCoreManagers();
            
            // Initialize optional systems
            InitializeOptionalSystems();
            
            // Apply initial settings
            ApplyGameSettings();
            
            // Set up scene
            SetupScene();
            
            Debug.Log("GameInitializer: Game initialization complete!");
        }
        
        private void InitializeCoreManagers()
        {
            // GameManager
            if (GameManager.Instance == null)
            {
                if (gameManagerPrefab != null)
                {
                    Instantiate(gameManagerPrefab);
                }
                else
                {
                    CreateGameManager();
                }
            }
            
            // AudioManager
            if (AudioManager.Instance == null)
            {
                if (audioManagerPrefab != null)
                {
                    Instantiate(audioManagerPrefab);
                }
                else
                {
                    CreateAudioManager();
                }
            }
            
            // UIManager
            if (UIManager.Instance == null)
            {
                if (uiManagerPrefab != null)
                {
                    Instantiate(uiManagerPrefab);
                }
                else
                {
                    CreateUIManager();
                }
            }
            
            // SaveSystem
            if (SaveSystem.Instance == null)
            {
                if (saveSystemPrefab != null)
                {
                    Instantiate(saveSystemPrefab);
                }
                else
                {
                    CreateSaveSystem();
                }
            }
            
            // InputManager
            if (InputManager.Instance == null)
            {
                if (inputManagerPrefab != null)
                {
                    Instantiate(inputManagerPrefab);
                }
                else
                {
                    CreateInputManager();
                }
            }
        }
        
        private void InitializeOptionalSystems()
        {
            // ObjectPooler
            if (ObjectPooler.Instance == null)
            {
                if (objectPoolerPrefab != null)
                {
                    Instantiate(objectPoolerPrefab);
                }
                else
                {
                    CreateObjectPooler();
                }
            }
            
            // PerformanceMonitor
            if (enablePerformanceMonitoring)
            {
                if (performanceMonitorPrefab != null)
                {
                    Instantiate(performanceMonitorPrefab);
                }
                else
                {
                    CreatePerformanceMonitor();
                }
            }
            
            // GameTester
            if (enableTesting)
            {
                if (gameTesterPrefab != null)
                {
                    Instantiate(gameTesterPrefab);
                }
                else
                {
                    CreateGameTester();
                }
            }
        }
        
        private void ApplyGameSettings()
        {
            // Apply game settings from GameSettings ScriptableObject
            GameSettings settings = GameSettings.Instance;
            if (settings != null)
            {
                settings.ApplyGraphicsSettings();
                settings.LoadSettings();
            }
            
            // Set cursor state
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
            
            // Set time scale
            Time.timeScale = 1f;
        }
        
        private void SetupScene()
        {
            // Ensure there's a main camera
            if (Camera.main == null)
            {
                CreateMainCamera();
            }
            
            // Ensure there's an EventSystem for UI
            if (FindObjectOfType<UnityEngine.EventSystems.EventSystem>() == null)
            {
                CreateEventSystem();
            }
            
            // Set up lighting if needed
            SetupLighting();
        }
        
        // Creation methods for core systems
        private void CreateGameManager()
        {
            GameObject go = new GameObject("GameManager");
            go.AddComponent<GameManager>();
            Debug.Log("GameInitializer: Created GameManager");
        }
        
        private void CreateAudioManager()
        {
            GameObject go = new GameObject("AudioManager");
            go.AddComponent<AudioManager>();
            Debug.Log("GameInitializer: Created AudioManager");
        }
        
        private void CreateUIManager()
        {
            // Create Canvas first
            GameObject canvasGO = new GameObject("Canvas");
            Canvas canvas = canvasGO.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasGO.AddComponent<UnityEngine.UI.CanvasScaler>();
            canvasGO.AddComponent<UnityEngine.UI.GraphicRaycaster>();
            
            // Create UIManager
            GameObject go = new GameObject("UIManager");
            go.AddComponent<UIManager>();
            Debug.Log("GameInitializer: Created UIManager");
        }
        
        private void CreateSaveSystem()
        {
            GameObject go = new GameObject("SaveSystem");
            go.AddComponent<SaveSystem>();
            Debug.Log("GameInitializer: Created SaveSystem");
        }
        
        private void CreateInputManager()
        {
            GameObject go = new GameObject("InputManager");
            go.AddComponent<InputManager>();
            Debug.Log("GameInitializer: Created InputManager");
        }
        
        private void CreateObjectPooler()
        {
            GameObject go = new GameObject("ObjectPooler");
            go.AddComponent<ObjectPooler>();
            Debug.Log("GameInitializer: Created ObjectPooler");
        }
        
        private void CreatePerformanceMonitor()
        {
            GameObject go = new GameObject("PerformanceMonitor");
            go.AddComponent<PerformanceMonitor>();
            Debug.Log("GameInitializer: Created PerformanceMonitor");
        }
        
        private void CreateGameTester()
        {
            GameObject go = new GameObject("GameTester");
            go.AddComponent<GameTester>();
            Debug.Log("GameInitializer: Created GameTester");
        }
        
        private void CreateMainCamera()
        {
            GameObject cameraGO = new GameObject("Main Camera");
            cameraGO.tag = "MainCamera";
            Camera camera = cameraGO.AddComponent<Camera>();
            cameraGO.AddComponent<AudioListener>();
            
            // Position camera
            cameraGO.transform.position = new Vector3(0, 5, -10);
            cameraGO.transform.rotation = Quaternion.Euler(15, 0, 0);
            
            Debug.Log("GameInitializer: Created Main Camera");
        }
        
        private void CreateEventSystem()
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystemGO.AddComponent<UnityEngine.EventSystems.EventSystem>();
            eventSystemGO.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
            
            Debug.Log("GameInitializer: Created EventSystem");
        }
        
        private void SetupLighting()
        {
            // Set ambient lighting
            RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
            RenderSettings.ambientSkyColor = new Color(0.5f, 0.7f, 1f);
            RenderSettings.ambientEquatorColor = new Color(0.4f, 0.4f, 0.4f);
            RenderSettings.ambientGroundColor = new Color(0.2f, 0.2f, 0.2f);
            
            // Create directional light if none exists
            Light[] lights = FindObjectsOfType<Light>();
            bool hasDirectionalLight = false;
            
            foreach (Light light in lights)
            {
                if (light.type == LightType.Directional)
                {
                    hasDirectionalLight = true;
                    break;
                }
            }
            
            if (!hasDirectionalLight)
            {
                GameObject lightGO = new GameObject("Directional Light");
                Light directionalLight = lightGO.AddComponent<Light>();
                directionalLight.type = LightType.Directional;
                directionalLight.color = Color.white;
                directionalLight.intensity = 1f;
                directionalLight.shadows = LightShadows.Soft;
                lightGO.transform.rotation = Quaternion.Euler(50f, -30f, 0f);
                
                Debug.Log("GameInitializer: Created Directional Light");
            }
            
            // Enable fog
            RenderSettings.fog = true;
            RenderSettings.fogColor = Color.gray;
            RenderSettings.fogMode = FogMode.ExponentialSquared;
            RenderSettings.fogDensity = 0.01f;
        }
        
        public void EnableDebugMode()
        {
            enableDebugMode = true;
            Debug.Log("GameInitializer: Debug mode enabled");
            
            // Enable additional debug features
            if (enableTesting && FindObjectOfType<GameTester>() == null)
            {
                CreateGameTester();
            }
            
            if (enablePerformanceMonitoring && FindObjectOfType<PerformanceMonitor>() == null)
            {
                CreatePerformanceMonitor();
            }
        }
        
        public void DisableDebugMode()
        {
            enableDebugMode = false;
            Debug.Log("GameInitializer: Debug mode disabled");
        }
        
        private void OnValidate()
        {
            // Ensure settings are valid
            if (enableTesting && !enableDebugMode)
            {
                Debug.LogWarning("GameInitializer: Testing is enabled but debug mode is not. Consider enabling debug mode for testing.");
            }
        }
    }
}
