using UnityEngine;

namespace CrystalQuest.Environment
{
    /// <summary>
    /// Procedural terrain generation system for creating varied 3D landscapes
    /// </summary>
    public class TerrainGenerator : MonoBehaviour
    {
        [Header("Terrain Settings")]
        [SerializeField] private int terrainWidth = 100;
        [SerializeField] private int terrainHeight = 100;
        [SerializeField] private float terrainScale = 20f;
        [SerializeField] private float heightScale = 5f;
        
        [Header("Noise Settings")]
        [SerializeField] private float noiseScale = 0.1f;
        [SerializeField] private int octaves = 4;
        [SerializeField] private float persistence = 0.5f;
        [SerializeField] private float lacunarity = 2f;
        [SerializeField] private Vector2 offset = Vector2.zero;
        [SerializeField] private int seed = 0;
        
        [Header("Materials")]
        [SerializeField] private Material grassMaterial;
        [SerializeField] private Material rockMaterial;
        [SerializeField] private Material snowMaterial;
        [SerializeField] private Texture2D[] terrainTextures;
        
        [Header("Vegetation")]
        [SerializeField] private GameObject[] treePrefabs;
        [SerializeField] private GameObject[] grassPrefabs;
        [SerializeField] private GameObject[] rockPrefabs;
        [SerializeField] private float vegetationDensity = 0.1f;
        [SerializeField] private float minVegetationHeight = 0.3f;
        [SerializeField] private float maxVegetationHeight = 0.8f;
        
        private Terrain terrain;
        private TerrainData terrainData;
        
        private void Start()
        {
            if (GetComponent<Terrain>() == null)
            {
                GenerateTerrain();
            }
        }
        
        [ContextMenu("Generate Terrain")]
        public void GenerateTerrain()
        {
            CreateTerrainData();
            CreateTerrain();
            ApplyTextures();
            PlaceVegetation();
            
            Debug.Log("TerrainGenerator: Terrain generated successfully");
        }
        
        private void CreateTerrainData()
        {
            terrainData = new TerrainData();
            terrainData.heightmapResolution = terrainWidth + 1;
            terrainData.size = new Vector3(terrainWidth, heightScale, terrainHeight);
            
            // Generate height map
            float[,] heights = GenerateHeights();
            terrainData.SetHeights(0, 0, heights);
        }
        
        private float[,] GenerateHeights()
        {
            float[,] heights = new float[terrainWidth + 1, terrainHeight + 1];
            
            // Use random seed if specified
            if (seed != 0)
            {
                Random.InitState(seed);
            }
            
            for (int x = 0; x <= terrainWidth; x++)
            {
                for (int y = 0; y <= terrainHeight; y++)
                {
                    float height = GeneratePerlinNoise(x, y);
                    heights[x, y] = height;
                }
            }
            
            return heights;
        }
        
        private float GeneratePerlinNoise(int x, int y)
        {
            float amplitude = 1f;
            float frequency = noiseScale;
            float noiseHeight = 0f;
            float maxValue = 0f;
            
            for (int i = 0; i < octaves; i++)
            {
                float sampleX = (x + offset.x) * frequency;
                float sampleY = (y + offset.y) * frequency;
                
                float perlinValue = Mathf.PerlinNoise(sampleX, sampleY) * 2 - 1;
                noiseHeight += perlinValue * amplitude;
                
                maxValue += amplitude;
                amplitude *= persistence;
                frequency *= lacunarity;
            }
            
            return noiseHeight / maxValue;
        }
        
        private void CreateTerrain()
        {
            // Remove existing terrain if present
            Terrain existingTerrain = GetComponent<Terrain>();
            if (existingTerrain != null)
            {
                DestroyImmediate(existingTerrain);
            }
            
            TerrainCollider existingCollider = GetComponent<TerrainCollider>();
            if (existingCollider != null)
            {
                DestroyImmediate(existingCollider);
            }
            
            // Create new terrain
            terrain = gameObject.AddComponent<Terrain>();
            terrain.terrainData = terrainData;
            
            // Add terrain collider
            TerrainCollider terrainCollider = gameObject.AddComponent<TerrainCollider>();
            terrainCollider.terrainData = terrainData;
        }
        
        private void ApplyTextures()
        {
            if (terrainTextures == null || terrainTextures.Length == 0)
            {
                CreateDefaultTextures();
                return;
            }
            
            // Create terrain layers
            TerrainLayer[] terrainLayers = new TerrainLayer[terrainTextures.Length];
            
            for (int i = 0; i < terrainTextures.Length; i++)
            {
                terrainLayers[i] = new TerrainLayer();
                terrainLayers[i].diffuseTexture = terrainTextures[i];
                terrainLayers[i].tileSize = new Vector2(15, 15);
            }
            
            terrainData.terrainLayers = terrainLayers;
            
            // Apply texture blending based on height and slope
            ApplyTextureBlending();
        }
        
        private void CreateDefaultTextures()
        {
            // Create simple colored textures if none are provided
            terrainTextures = new Texture2D[3];
            
            // Grass texture (green)
            terrainTextures[0] = CreateColorTexture(new Color(0.3f, 0.6f, 0.3f));
            
            // Rock texture (gray)
            terrainTextures[1] = CreateColorTexture(new Color(0.5f, 0.5f, 0.5f));
            
            // Snow texture (white)
            terrainTextures[2] = CreateColorTexture(Color.white);
            
            ApplyTextures();
        }
        
        private Texture2D CreateColorTexture(Color color)
        {
            Texture2D texture = new Texture2D(64, 64);
            Color[] pixels = new Color[64 * 64];
            
            for (int i = 0; i < pixels.Length; i++)
            {
                pixels[i] = color;
            }
            
            texture.SetPixels(pixels);
            texture.Apply();
            
            return texture;
        }
        
        private void ApplyTextureBlending()
        {
            float[,,] alphaMap = new float[terrainData.alphamapWidth, terrainData.alphamapHeight, terrainData.alphamapLayers];
            
            for (int x = 0; x < terrainData.alphamapWidth; x++)
            {
                for (int y = 0; y < terrainData.alphamapHeight; y++)
                {
                    // Get height at this position
                    float normalizedX = (float)x / (terrainData.alphamapWidth - 1);
                    float normalizedY = (float)y / (terrainData.alphamapHeight - 1);
                    float height = terrainData.GetInterpolatedHeight(normalizedX, normalizedY) / terrainData.size.y;
                    
                    // Get slope at this position
                    float slope = terrainData.GetSteepness(normalizedX, normalizedY);
                    
                    // Determine texture weights based on height and slope
                    float[] weights = new float[terrainData.alphamapLayers];
                    
                    if (terrainData.alphamapLayers >= 3)
                    {
                        // Grass (low areas, gentle slopes)
                        weights[0] = Mathf.Clamp01(1f - height) * Mathf.Clamp01(1f - slope / 45f);
                        
                        // Rock (steep slopes)
                        weights[1] = Mathf.Clamp01(slope / 45f);
                        
                        // Snow (high areas)
                        weights[2] = Mathf.Clamp01(height - 0.6f) * 2f;
                    }
                    else if (terrainData.alphamapLayers >= 2)
                    {
                        weights[0] = 1f - height;
                        weights[1] = height;
                    }
                    else
                    {
                        weights[0] = 1f;
                    }
                    
                    // Normalize weights
                    float totalWeight = 0f;
                    for (int i = 0; i < weights.Length; i++)
                    {
                        totalWeight += weights[i];
                    }
                    
                    if (totalWeight > 0f)
                    {
                        for (int i = 0; i < weights.Length; i++)
                        {
                            weights[i] /= totalWeight;
                            alphaMap[x, y, i] = weights[i];
                        }
                    }
                }
            }
            
            terrainData.SetAlphamaps(0, 0, alphaMap);
        }
        
        private void PlaceVegetation()
        {
            if (treePrefabs == null || treePrefabs.Length == 0) return;
            
            // Clear existing vegetation
            ClearVegetation();
            
            int vegetationCount = Mathf.RoundToInt(terrainWidth * terrainHeight * vegetationDensity);
            
            for (int i = 0; i < vegetationCount; i++)
            {
                // Random position on terrain
                float x = Random.Range(0f, terrainWidth);
                float z = Random.Range(0f, terrainHeight);
                
                // Get height at this position
                float normalizedX = x / terrainWidth;
                float normalizedZ = z / terrainHeight;
                float height = terrainData.GetInterpolatedHeight(normalizedX, normalizedZ) / terrainData.size.y;
                
                // Only place vegetation in suitable height range
                if (height >= minVegetationHeight && height <= maxVegetationHeight)
                {
                    // Get slope to avoid placing on steep areas
                    float slope = terrainData.GetSteepness(normalizedX, normalizedZ);
                    
                    if (slope < 30f) // Not too steep
                    {
                        Vector3 worldPosition = new Vector3(x, terrainData.GetInterpolatedHeight(normalizedX, normalizedZ), z);
                        worldPosition += transform.position;
                        
                        // Choose random vegetation prefab
                        GameObject prefab = treePrefabs[Random.Range(0, treePrefabs.Length)];
                        
                        // Instantiate with random rotation
                        Quaternion rotation = Quaternion.Euler(0, Random.Range(0f, 360f), 0);
                        GameObject vegetation = Instantiate(prefab, worldPosition, rotation, transform);
                        
                        // Random scale variation
                        float scale = Random.Range(0.8f, 1.2f);
                        vegetation.transform.localScale = Vector3.one * scale;
                    }
                }
            }
        }
        
        private void ClearVegetation()
        {
            // Remove all child objects (vegetation)
            for (int i = transform.childCount - 1; i >= 0; i--)
            {
                DestroyImmediate(transform.GetChild(i).gameObject);
            }
        }
        
        [ContextMenu("Clear Terrain")]
        public void ClearTerrain()
        {
            ClearVegetation();
            
            Terrain terrainComponent = GetComponent<Terrain>();
            if (terrainComponent != null)
            {
                DestroyImmediate(terrainComponent);
            }
            
            TerrainCollider colliderComponent = GetComponent<TerrainCollider>();
            if (colliderComponent != null)
            {
                DestroyImmediate(colliderComponent);
            }
            
            Debug.Log("TerrainGenerator: Terrain cleared");
        }
        
        private void OnValidate()
        {
            terrainWidth = Mathf.Clamp(terrainWidth, 10, 500);
            terrainHeight = Mathf.Clamp(terrainHeight, 10, 500);
            terrainScale = Mathf.Max(1f, terrainScale);
            heightScale = Mathf.Max(0.1f, heightScale);
            noiseScale = Mathf.Clamp(noiseScale, 0.001f, 1f);
            octaves = Mathf.Clamp(octaves, 1, 8);
            persistence = Mathf.Clamp01(persistence);
            lacunarity = Mathf.Max(1f, lacunarity);
            vegetationDensity = Mathf.Clamp(vegetationDensity, 0f, 1f);
        }
    }
}
