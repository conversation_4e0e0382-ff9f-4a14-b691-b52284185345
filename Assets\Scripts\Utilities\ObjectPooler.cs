using System.Collections.Generic;
using UnityEngine;

namespace CrystalQuest.Utilities
{
    /// <summary>
    /// Generic object pooling system for performance optimization
    /// </summary>
    public class ObjectPooler : MonoBehaviour
    {
        [System.Serializable]
        public class Pool
        {
            public string tag;
            public GameObject prefab;
            public int size;
            public bool expandable = true;
        }
        
        [Header("Object Pools")]
        [SerializeField] private List<Pool> pools = new List<Pool>();
        
        // Singleton instance
        public static ObjectPooler Instance { get; private set; }
        
        // Dictionary to store pools
        private Dictionary<string, Queue<GameObject>> poolDictionary;
        private Dictionary<string, Pool> poolSettings;
        
        private void Awake()
        {
            // Implement singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializePools();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializePools()
        {
            poolDictionary = new Dictionary<string, Queue<GameObject>>();
            poolSettings = new Dictionary<string, Pool>();
            
            foreach (Pool pool in pools)
            {
                Queue<GameObject> objectPool = new Queue<GameObject>();
                poolSettings[pool.tag] = pool;
                
                // Create parent object for organization
                GameObject poolParent = new GameObject($"Pool_{pool.tag}");
                poolParent.transform.SetParent(transform);
                
                // Pre-instantiate objects
                for (int i = 0; i < pool.size; i++)
                {
                    GameObject obj = CreatePooledObject(pool.prefab, poolParent.transform);
                    objectPool.Enqueue(obj);
                }
                
                poolDictionary.Add(pool.tag, objectPool);
            }
            
            Debug.Log($"ObjectPooler: Initialized {pools.Count} pools");
        }
        
        private GameObject CreatePooledObject(GameObject prefab, Transform parent)
        {
            GameObject obj = Instantiate(prefab, parent);
            obj.SetActive(false);
            
            // Add PooledObject component if it doesn't exist
            if (obj.GetComponent<PooledObject>() == null)
            {
                obj.AddComponent<PooledObject>();
            }
            
            return obj;
        }
        
        public GameObject SpawnFromPool(string tag, Vector3 position, Quaternion rotation)
        {
            if (!poolDictionary.ContainsKey(tag))
            {
                Debug.LogWarning($"ObjectPooler: Pool with tag '{tag}' doesn't exist!");
                return null;
            }
            
            GameObject objectToSpawn = null;
            Queue<GameObject> pool = poolDictionary[tag];
            
            // Try to get an inactive object from the pool
            int attempts = 0;
            while (attempts < pool.Count)
            {
                GameObject obj = pool.Dequeue();
                pool.Enqueue(obj); // Put it back in the queue
                
                if (!obj.activeInHierarchy)
                {
                    objectToSpawn = obj;
                    break;
                }
                
                attempts++;
            }
            
            // If no inactive object found and pool is expandable, create a new one
            if (objectToSpawn == null && poolSettings[tag].expandable)
            {
                Pool poolSetting = poolSettings[tag];
                Transform poolParent = transform.Find($"Pool_{tag}");
                objectToSpawn = CreatePooledObject(poolSetting.prefab, poolParent);
                pool.Enqueue(objectToSpawn);
                
                Debug.Log($"ObjectPooler: Expanded pool '{tag}' - new size: {pool.Count}");
            }
            
            if (objectToSpawn != null)
            {
                objectToSpawn.transform.position = position;
                objectToSpawn.transform.rotation = rotation;
                objectToSpawn.SetActive(true);
                
                // Notify the object it was spawned
                PooledObject pooledObj = objectToSpawn.GetComponent<PooledObject>();
                if (pooledObj != null)
                {
                    pooledObj.OnObjectSpawn();
                }
            }
            else
            {
                Debug.LogWarning($"ObjectPooler: No available objects in pool '{tag}' and pool is not expandable!");
            }
            
            return objectToSpawn;
        }
        
        public void ReturnToPool(string tag, GameObject obj)
        {
            if (obj == null) return;
            
            obj.SetActive(false);
            
            // Notify the object it was returned
            PooledObject pooledObj = obj.GetComponent<PooledObject>();
            if (pooledObj != null)
            {
                pooledObj.OnObjectReturn();
            }
        }
        
        public void ReturnToPool(GameObject obj)
        {
            if (obj == null) return;
            
            // Try to find which pool this object belongs to
            string poolTag = FindPoolTag(obj);
            if (!string.IsNullOrEmpty(poolTag))
            {
                ReturnToPool(poolTag, obj);
            }
            else
            {
                Debug.LogWarning($"ObjectPooler: Could not find pool for object '{obj.name}'");
                obj.SetActive(false);
            }
        }
        
        private string FindPoolTag(GameObject obj)
        {
            foreach (var kvp in poolDictionary)
            {
                if (kvp.Value.Contains(obj))
                {
                    return kvp.Key;
                }
            }
            return null;
        }
        
        public void ClearPool(string tag)
        {
            if (!poolDictionary.ContainsKey(tag)) return;
            
            Queue<GameObject> pool = poolDictionary[tag];
            while (pool.Count > 0)
            {
                GameObject obj = pool.Dequeue();
                if (obj != null)
                {
                    DestroyImmediate(obj);
                }
            }
            
            Debug.Log($"ObjectPooler: Cleared pool '{tag}'");
        }
        
        public void ClearAllPools()
        {
            foreach (string tag in poolDictionary.Keys)
            {
                ClearPool(tag);
            }
        }
        
        public int GetPoolSize(string tag)
        {
            if (poolDictionary.ContainsKey(tag))
            {
                return poolDictionary[tag].Count;
            }
            return 0;
        }
        
        public int GetActiveCount(string tag)
        {
            if (!poolDictionary.ContainsKey(tag)) return 0;
            
            int activeCount = 0;
            foreach (GameObject obj in poolDictionary[tag])
            {
                if (obj.activeInHierarchy)
                {
                    activeCount++;
                }
            }
            return activeCount;
        }
        
        private void OnDestroy()
        {
            ClearAllPools();
        }
    }
    
    /// <summary>
    /// Component that should be attached to pooled objects
    /// </summary>
    public class PooledObject : MonoBehaviour
    {
        [Header("Pool Settings")]
        [SerializeField] private bool autoReturnToPool = false;
        [SerializeField] private float autoReturnTime = 5f;
        
        private float spawnTime;
        
        public virtual void OnObjectSpawn()
        {
            spawnTime = Time.time;
            
            // Reset any object-specific state here
            ResetObject();
        }
        
        public virtual void OnObjectReturn()
        {
            // Clean up any object-specific state here
            CleanupObject();
        }
        
        protected virtual void ResetObject()
        {
            // Override in derived classes to reset object state
            // For example: reset health, position, velocity, etc.
        }
        
        protected virtual void CleanupObject()
        {
            // Override in derived classes to cleanup object state
            // For example: stop particles, reset animations, etc.
        }
        
        private void Update()
        {
            if (autoReturnToPool && Time.time - spawnTime >= autoReturnTime)
            {
                ReturnToPool();
            }
        }
        
        public void ReturnToPool()
        {
            if (ObjectPooler.Instance != null)
            {
                ObjectPooler.Instance.ReturnToPool(gameObject);
            }
            else
            {
                gameObject.SetActive(false);
            }
        }
        
        public void ReturnToPoolAfterDelay(float delay)
        {
            Invoke(nameof(ReturnToPool), delay);
        }
    }
}
