using UnityEngine;
using UnityEngine.UI;
using TMPro;
using CrystalQuest.Core;
using CrystalQuest.Gameplay;

namespace CrystalQuest.UI
{
    /// <summary>
    /// Controls the in-game HUD elements and real-time UI updates
    /// </summary>
    public class HUDController : MonoBeh<PERSON>our
    {
        [Header("Health Display")]
        [SerializeField] private Slider healthSlider;
        [SerializeField] private TextMeshProUGUI healthText;
        [SerializeField] private Image healthFillImage;
        [SerializeField] private Gradient healthColorGradient;
        
        [Header("Score Display")]
        [SerializeField] private TextMeshProUGUI scoreText;
        [SerializeField] private TextMeshP<PERSON><PERSON>GUI multiplierText;
        [SerializeField] private TextMeshProUGUI comboText;
        [SerializeField] private GameObject comboContainer;
        
        [Header("Crystal Progress")]
        [SerializeField] private TextMeshProUGUI crystalText;
        [SerializeField] private Slider crystalProgressSlider;
        [Serial<PERSON>Field] private Image[] crystalIcons;
        
        [Header("Objectives")]
        [SerializeField] private Transform objectiveContainer;
        [SerializeField] private GameObject objectivePrefab;
        [SerializeField] private int maxVisibleObjectives = 3;
        
        [Header("Minimap")]
        [SerializeField] private RawImage minimapImage;
        [SerializeField] private Transform minimapPlayerIcon;
        [SerializeField] private Transform minimapCrystalContainer;
        [SerializeField] private GameObject minimapCrystalIconPrefab;
        
        [Header("Crosshair")]
        [SerializeField] private Image crosshairImage;
        [SerializeField] private bool dynamicCrosshair = true;
        [SerializeField] private float crosshairExpandDistance = 10f;
        
        [Header("Interaction Prompt")]
        [SerializeField] private GameObject interactionPrompt;
        [SerializeField] private TextMeshProUGUI interactionText;
        [SerializeField] private float interactionRange = 3f;
        
        [Header("Timer")]
        [SerializeField] private TextMeshProUGUI timerText;
        [SerializeField] private bool showTimer = false;
        
        [Header("Animation")]
        [SerializeField] private bool enableAnimations = true;
        [SerializeField] private float animationDuration = 0.3f;
        
        // Private variables
        private Camera playerCamera;
        private Transform playerTransform;
        private System.Collections.Generic.List<GameObject> objectiveUIElements = new System.Collections.Generic.List<GameObject>();
        private System.Collections.Generic.List<GameObject> minimapCrystalIcons = new System.Collections.Generic.List<GameObject>();
        private float lastHealthValue = 1f;
        private int lastScore = 0;
        private float lastMultiplier = 1f;
        private int lastCombo = 0;
        
        private void Start()
        {
            InitializeHUD();
            SubscribeToEvents();
        }
        
        private void InitializeHUD()
        {
            // Find player components
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerTransform = player.transform;
            }
            
            playerCamera = Camera.main;
            
            // Initialize UI elements
            if (healthSlider != null)
            {
                healthSlider.value = 1f;
            }
            
            if (comboContainer != null)
            {
                comboContainer.SetActive(false);
            }
            
            if (interactionPrompt != null)
            {
                interactionPrompt.SetActive(false);
            }
            
            // Set up health color gradient if not configured
            if (healthColorGradient == null || healthColorGradient.colorKeys.Length == 0)
            {
                healthColorGradient = new Gradient();
                GradientColorKey[] colorKeys = new GradientColorKey[3];
                colorKeys[0] = new GradientColorKey(Color.red, 0f);
                colorKeys[1] = new GradientColorKey(Color.yellow, 0.5f);
                colorKeys[2] = new GradientColorKey(Color.green, 1f);
                
                GradientAlphaKey[] alphaKeys = new GradientAlphaKey[2];
                alphaKeys[0] = new GradientAlphaKey(1f, 0f);
                alphaKeys[1] = new GradientAlphaKey(1f, 1f);
                
                healthColorGradient.SetKeys(colorKeys, alphaKeys);
            }
            
            UpdateAllDisplays();
        }
        
        private void SubscribeToEvents()
        {
            // Subscribe to game manager events
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnScoreChanged += UpdateScore;
                GameManager.Instance.OnCrystalsChanged += UpdateCrystals;
            }
            
            // Subscribe to score manager events
            if (ScoreManager.Instance != null)
            {
                ScoreManager.Instance.OnMultiplierChanged += UpdateMultiplier;
                ScoreManager.Instance.OnComboChanged += UpdateCombo;
            }
            
            // Subscribe to objective manager events
            if (ObjectiveManager.Instance != null)
            {
                ObjectiveManager.Instance.OnObjectiveAdded += OnObjectiveAdded;
                ObjectiveManager.Instance.OnObjectiveCompleted += OnObjectiveCompleted;
                ObjectiveManager.Instance.OnObjectiveUpdated += OnObjectiveUpdated;
            }
        }
        
        private void Update()
        {
            UpdateInteractionPrompt();
            UpdateCrosshair();
            UpdateTimer();
            UpdateMinimap();
        }
        
        private void UpdateAllDisplays()
        {
            // Update with current values
            if (GameManager.Instance != null)
            {
                UpdateScore(GameManager.Instance.PlayerScore);
                UpdateCrystals(GameManager.Instance.CrystalsCollected, GameManager.Instance.TotalCrystals);
            }
            
            if (ScoreManager.Instance != null)
            {
                UpdateMultiplier(ScoreManager.Instance.CurrentMultiplier);
                UpdateCombo(ScoreManager.Instance.CurrentCombo);
            }
            
            UpdateHealth(1f); // Default full health
        }
        
        public void UpdateHealth(float healthPercentage)
        {
            if (healthSlider != null)
            {
                if (enableAnimations)
                {
                    StartCoroutine(AnimateSlider(healthSlider, healthPercentage));
                }
                else
                {
                    healthSlider.value = healthPercentage;
                }
            }
            
            if (healthText != null)
            {
                int currentHealth = Mathf.RoundToInt(healthPercentage * 100);
                healthText.text = $"{currentHealth}%";
            }
            
            if (healthFillImage != null)
            {
                healthFillImage.color = healthColorGradient.Evaluate(healthPercentage);
            }
            
            lastHealthValue = healthPercentage;
        }
        
        private void UpdateScore(int score)
        {
            if (scoreText != null)
            {
                if (enableAnimations && score != lastScore)
                {
                    StartCoroutine(AnimateScore(lastScore, score));
                }
                else
                {
                    scoreText.text = $"Score: {score:N0}";
                }
            }
            
            lastScore = score;
        }
        
        private void UpdateMultiplier(float multiplier)
        {
            if (multiplierText != null)
            {
                multiplierText.text = $"x{multiplier:F1}";
                
                // Highlight multiplier when it's above 1
                if (multiplier > 1f)
                {
                    multiplierText.color = Color.yellow;
                    if (enableAnimations)
                    {
                        StartCoroutine(PulseElement(multiplierText.transform));
                    }
                }
                else
                {
                    multiplierText.color = Color.white;
                }
            }
            
            lastMultiplier = multiplier;
        }
        
        private void UpdateCombo(int combo)
        {
            if (comboText != null && comboContainer != null)
            {
                if (combo > 0)
                {
                    comboContainer.SetActive(true);
                    comboText.text = $"Combo: {combo}";
                    
                    if (enableAnimations && combo > lastCombo)
                    {
                        StartCoroutine(PulseElement(comboContainer.transform));
                    }
                }
                else
                {
                    comboContainer.SetActive(false);
                }
            }
            
            lastCombo = combo;
        }
        
        private void UpdateCrystals(int collected, int total)
        {
            if (crystalText != null)
            {
                crystalText.text = $"Crystals: {collected}/{total}";
            }
            
            if (crystalProgressSlider != null)
            {
                float progress = total > 0 ? (float)collected / total : 0f;
                if (enableAnimations)
                {
                    StartCoroutine(AnimateSlider(crystalProgressSlider, progress));
                }
                else
                {
                    crystalProgressSlider.value = progress;
                }
            }
            
            // Update crystal icons
            UpdateCrystalIcons(collected, total);
        }
        
        private void UpdateCrystalIcons(int collected, int total)
        {
            if (crystalIcons == null) return;
            
            for (int i = 0; i < crystalIcons.Length; i++)
            {
                if (crystalIcons[i] != null)
                {
                    if (i < total)
                    {
                        crystalIcons[i].gameObject.SetActive(true);
                        crystalIcons[i].color = i < collected ? Color.cyan : Color.gray;
                    }
                    else
                    {
                        crystalIcons[i].gameObject.SetActive(false);
                    }
                }
            }
        }
        
        private void UpdateInteractionPrompt()
        {
            if (interactionPrompt == null || playerCamera == null) return;
            
            // Raycast to check for interactable objects
            Ray ray = new Ray(playerCamera.transform.position, playerCamera.transform.forward);
            RaycastHit hit;
            
            if (Physics.Raycast(ray, out hit, interactionRange))
            {
                var interactable = hit.collider.GetComponent<CrystalQuest.Player.IInteractable>();
                if (interactable != null)
                {
                    ShowInteractionPrompt("Press E to interact");
                    return;
                }
                
                var crystal = hit.collider.GetComponent<CrystalQuest.Player.Crystal>();
                if (crystal != null && !crystal.IsCollected)
                {
                    ShowInteractionPrompt("Press E to collect crystal");
                    return;
                }
            }
            
            HideInteractionPrompt();
        }
        
        private void ShowInteractionPrompt(string text)
        {
            if (interactionPrompt != null && !interactionPrompt.activeInHierarchy)
            {
                interactionPrompt.SetActive(true);
            }
            
            if (interactionText != null)
            {
                interactionText.text = text;
            }
        }
        
        private void HideInteractionPrompt()
        {
            if (interactionPrompt != null && interactionPrompt.activeInHierarchy)
            {
                interactionPrompt.SetActive(false);
            }
        }
        
        private void UpdateCrosshair()
        {
            if (!dynamicCrosshair || crosshairImage == null || playerCamera == null) return;
            
            // Check if aiming at something
            Ray ray = new Ray(playerCamera.transform.position, playerCamera.transform.forward);
            RaycastHit hit;
            
            if (Physics.Raycast(ray, out hit, crosshairExpandDistance))
            {
                // Expand crosshair when aiming at objects
                crosshairImage.transform.localScale = Vector3.one * 1.2f;
                crosshairImage.color = Color.red;
            }
            else
            {
                // Normal crosshair
                crosshairImage.transform.localScale = Vector3.one;
                crosshairImage.color = Color.white;
            }
        }
        
        private void UpdateTimer()
        {
            if (!showTimer || timerText == null) return;
            
            // Get time from level manager or game manager
            // This is a placeholder - you'd implement based on your time system
            float gameTime = Time.time;
            int minutes = Mathf.FloorToInt(gameTime / 60f);
            int seconds = Mathf.FloorToInt(gameTime % 60f);
            
            timerText.text = $"{minutes:00}:{seconds:00}";
        }
        
        private void UpdateMinimap()
        {
            if (minimapPlayerIcon == null || playerTransform == null) return;
            
            // Update player icon position on minimap
            // This is a simplified version - you'd implement based on your minimap system
            Vector3 playerPos = playerTransform.position;
            // Convert world position to minimap position
            // minimapPlayerIcon.localPosition = WorldToMinimapPosition(playerPos);
        }
        
        // Objective UI methods
        private void OnObjectiveAdded(Objective objective)
        {
            if (objectiveContainer == null || objectivePrefab == null) return;
            
            if (objectiveUIElements.Count >= maxVisibleObjectives) return;
            
            GameObject objectiveUI = Instantiate(objectivePrefab, objectiveContainer);
            UpdateObjectiveUI(objectiveUI, objective);
            objectiveUIElements.Add(objectiveUI);
        }
        
        private void OnObjectiveCompleted(Objective objective)
        {
            // Find and remove the objective UI
            for (int i = objectiveUIElements.Count - 1; i >= 0; i--)
            {
                GameObject objectiveUI = objectiveUIElements[i];
                if (objectiveUI.name.Contains(objective.id))
                {
                    if (enableAnimations)
                    {
                        StartCoroutine(AnimateObjectiveCompletion(objectiveUI));
                    }
                    else
                    {
                        Destroy(objectiveUI);
                        objectiveUIElements.RemoveAt(i);
                    }
                    break;
                }
            }
        }
        
        private void OnObjectiveUpdated(Objective objective)
        {
            // Find and update the objective UI
            foreach (GameObject objectiveUI in objectiveUIElements)
            {
                if (objectiveUI.name.Contains(objective.id))
                {
                    UpdateObjectiveUI(objectiveUI, objective);
                    break;
                }
            }
        }
        
        private void UpdateObjectiveUI(GameObject objectiveUI, Objective objective)
        {
            objectiveUI.name = $"Objective_{objective.id}";
            
            TextMeshProUGUI titleText = objectiveUI.transform.Find("Title")?.GetComponent<TextMeshProUGUI>();
            if (titleText != null)
            {
                titleText.text = objective.title;
            }
            
            TextMeshProUGUI progressText = objectiveUI.transform.Find("Progress")?.GetComponent<TextMeshProUGUI>();
            if (progressText != null)
            {
                progressText.text = objective.GetProgressText();
            }
            
            Slider progressSlider = objectiveUI.GetComponentInChildren<Slider>();
            if (progressSlider != null)
            {
                progressSlider.value = objective.Progress;
            }
        }
        
        // Animation coroutines
        private System.Collections.IEnumerator AnimateSlider(Slider slider, float targetValue)
        {
            float startValue = slider.value;
            float elapsed = 0f;
            
            while (elapsed < animationDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                slider.value = Mathf.Lerp(startValue, targetValue, elapsed / animationDuration);
                yield return null;
            }
            
            slider.value = targetValue;
        }
        
        private System.Collections.IEnumerator AnimateScore(int startScore, int targetScore)
        {
            float elapsed = 0f;
            
            while (elapsed < animationDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                int currentScore = Mathf.RoundToInt(Mathf.Lerp(startScore, targetScore, elapsed / animationDuration));
                scoreText.text = $"Score: {currentScore:N0}";
                yield return null;
            }
            
            scoreText.text = $"Score: {targetScore:N0}";
        }
        
        private System.Collections.IEnumerator PulseElement(Transform element)
        {
            Vector3 originalScale = element.localScale;
            Vector3 targetScale = originalScale * 1.2f;
            
            // Scale up
            float elapsed = 0f;
            while (elapsed < animationDuration * 0.5f)
            {
                elapsed += Time.unscaledDeltaTime;
                element.localScale = Vector3.Lerp(originalScale, targetScale, elapsed / (animationDuration * 0.5f));
                yield return null;
            }
            
            // Scale down
            elapsed = 0f;
            while (elapsed < animationDuration * 0.5f)
            {
                elapsed += Time.unscaledDeltaTime;
                element.localScale = Vector3.Lerp(targetScale, originalScale, elapsed / (animationDuration * 0.5f));
                yield return null;
            }
            
            element.localScale = originalScale;
        }
        
        private System.Collections.IEnumerator AnimateObjectiveCompletion(GameObject objectiveUI)
        {
            // Fade out and scale down
            CanvasGroup canvasGroup = objectiveUI.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                canvasGroup = objectiveUI.AddComponent<CanvasGroup>();
            }
            
            Vector3 originalScale = objectiveUI.transform.localScale;
            float elapsed = 0f;
            
            while (elapsed < animationDuration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / animationDuration;
                
                canvasGroup.alpha = 1f - progress;
                objectiveUI.transform.localScale = Vector3.Lerp(originalScale, Vector3.zero, progress);
                
                yield return null;
            }
            
            objectiveUIElements.Remove(objectiveUI);
            Destroy(objectiveUI);
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnScoreChanged -= UpdateScore;
                GameManager.Instance.OnCrystalsChanged -= UpdateCrystals;
            }
            
            if (ScoreManager.Instance != null)
            {
                ScoreManager.Instance.OnMultiplierChanged -= UpdateMultiplier;
                ScoreManager.Instance.OnComboChanged -= UpdateCombo;
            }
            
            if (ObjectiveManager.Instance != null)
            {
                ObjectiveManager.Instance.OnObjectiveAdded -= OnObjectiveAdded;
                ObjectiveManager.Instance.OnObjectiveCompleted -= OnObjectiveCompleted;
                ObjectiveManager.Instance.OnObjectiveUpdated -= OnObjectiveUpdated;
            }
        }
    }
}
