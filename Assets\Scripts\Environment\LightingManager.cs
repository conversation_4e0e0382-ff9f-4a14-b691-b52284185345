using UnityEngine;
using System.Collections;

namespace CrystalQuest.Environment
{
    /// <summary>
    /// Manages dynamic lighting, day/night cycles, and atmospheric effects
    /// </summary>
    public class LightingManager : MonoBehaviour
    {
        [Header("Sun Settings")]
        [SerializeField] private Light sunLight;
        [SerializeField] private Gradient sunColor;
        [SerializeField] private AnimationCurve sunIntensity;
        [SerializeField] private float sunRotationSpeed = 1f;
        
        [Header("Moon Settings")]
        [SerializeField] private Light moonLight;
        [SerializeField] private Color moonColor = Color.blue;
        [SerializeField] private float moonIntensity = 0.3f;
        
        [Header("Ambient Settings")]
        [SerializeField] private Gradient ambientColor;
        [SerializeField] private Gradient fogColor;
        [SerializeField] private AnimationCurve fogDensity;
        
        [Header("Skybox Settings")]
        [SerializeField] private Material daySkybox;
        [SerializeField] private Material nightSkybox;
        [SerializeField] private Material currentSkybox;
        
        [Header("Day/Night Cycle")]
        [SerializeField] private bool enableDayNightCycle = true;
        [SerializeField] private float dayDuration = 300f; // 5 minutes
        [SerializeField] private float currentTimeOfDay = 0.5f; // 0 = midnight, 0.5 = noon, 1 = midnight
        [SerializeField] private bool pauseTimeProgression = false;
        
        [Header("Weather Effects")]
        [SerializeField] private ParticleSystem rainEffect;
        [SerializeField] private ParticleSystem snowEffect;
        [SerializeField] private AudioSource weatherAudioSource;
        [SerializeField] private AudioClip rainSound;
        [SerializeField] private AudioClip windSound;
        
        // Private variables
        private float timeSpeed;
        private WeatherType currentWeather = WeatherType.Clear;
        private Coroutine weatherTransitionCoroutine;
        
        // Properties
        public float TimeOfDay => currentTimeOfDay;
        public bool IsDay => currentTimeOfDay > 0.25f && currentTimeOfDay < 0.75f;
        public bool IsNight => !IsDay;
        public WeatherType CurrentWeather => currentWeather;
        
        private void Awake()
        {
            // Calculate time speed
            timeSpeed = 1f / dayDuration;
            
            // Set up default components if not assigned
            SetupDefaultComponents();
        }
        
        private void Start()
        {
            // Initialize lighting
            UpdateLighting();
        }
        
        private void Update()
        {
            if (enableDayNightCycle && !pauseTimeProgression)
            {
                UpdateTimeOfDay();
            }
            
            UpdateLighting();
        }
        
        private void SetupDefaultComponents()
        {
            // Find sun light if not assigned
            if (sunLight == null)
            {
                Light[] lights = FindObjectsOfType<Light>();
                foreach (Light light in lights)
                {
                    if (light.type == LightType.Directional)
                    {
                        sunLight = light;
                        break;
                    }
                }
                
                // Create sun light if none found
                if (sunLight == null)
                {
                    GameObject sunGO = new GameObject("Sun Light");
                    sunLight = sunGO.AddComponent<Light>();
                    sunLight.type = LightType.Directional;
                    sunLight.shadows = LightShadows.Soft;
                }
            }
            
            // Create moon light if not assigned
            if (moonLight == null)
            {
                GameObject moonGO = new GameObject("Moon Light");
                moonLight = moonGO.AddComponent<Light>();
                moonLight.type = LightType.Directional;
                moonLight.color = moonColor;
                moonLight.intensity = moonIntensity;
                moonLight.shadows = LightShadows.Soft;
                moonLight.enabled = false;
            }
            
            // Set up default gradients if not configured
            SetupDefaultGradients();
        }
        
        private void SetupDefaultGradients()
        {
            if (sunColor == null || sunColor.colorKeys.Length == 0)
            {
                sunColor = new Gradient();
                GradientColorKey[] colorKeys = new GradientColorKey[4];
                colorKeys[0] = new GradientColorKey(new Color(0.2f, 0.2f, 0.4f), 0f); // Night
                colorKeys[1] = new GradientColorKey(new Color(1f, 0.6f, 0.3f), 0.25f); // Sunrise
                colorKeys[2] = new GradientColorKey(Color.white, 0.5f); // Noon
                colorKeys[3] = new GradientColorKey(new Color(1f, 0.4f, 0.2f), 0.75f); // Sunset
                
                GradientAlphaKey[] alphaKeys = new GradientAlphaKey[2];
                alphaKeys[0] = new GradientAlphaKey(1f, 0f);
                alphaKeys[1] = new GradientAlphaKey(1f, 1f);
                
                sunColor.SetKeys(colorKeys, alphaKeys);
            }
            
            if (ambientColor == null || ambientColor.colorKeys.Length == 0)
            {
                ambientColor = new Gradient();
                GradientColorKey[] colorKeys = new GradientColorKey[3];
                colorKeys[0] = new GradientColorKey(new Color(0.1f, 0.1f, 0.2f), 0f); // Night
                colorKeys[1] = new GradientColorKey(new Color(0.5f, 0.5f, 0.6f), 0.5f); // Day
                colorKeys[2] = new GradientColorKey(new Color(0.2f, 0.2f, 0.3f), 1f); // Night
                
                GradientAlphaKey[] alphaKeys = new GradientAlphaKey[2];
                alphaKeys[0] = new GradientAlphaKey(1f, 0f);
                alphaKeys[1] = new GradientAlphaKey(1f, 1f);
                
                ambientColor.SetKeys(colorKeys, alphaKeys);
            }
        }
        
        private void UpdateTimeOfDay()
        {
            currentTimeOfDay += timeSpeed * Time.deltaTime;
            
            if (currentTimeOfDay >= 1f)
            {
                currentTimeOfDay = 0f;
            }
        }
        
        private void UpdateLighting()
        {
            UpdateSunAndMoon();
            UpdateAmbientLighting();
            UpdateFog();
            UpdateSkybox();
        }
        
        private void UpdateSunAndMoon()
        {
            if (sunLight != null)
            {
                // Rotate sun based on time of day
                float sunAngle = (currentTimeOfDay - 0.25f) * 360f;
                sunLight.transform.rotation = Quaternion.Euler(sunAngle, 30f, 0f);
                
                // Update sun color and intensity
                sunLight.color = sunColor.Evaluate(currentTimeOfDay);
                sunLight.intensity = sunIntensity.Evaluate(currentTimeOfDay);
                
                // Enable/disable sun based on time
                sunLight.enabled = currentTimeOfDay > 0.2f && currentTimeOfDay < 0.8f;
            }
            
            if (moonLight != null)
            {
                // Position moon opposite to sun
                float moonAngle = sunLight.transform.eulerAngles.x + 180f;
                moonLight.transform.rotation = Quaternion.Euler(moonAngle, 30f, 0f);
                
                // Enable moon during night
                moonLight.enabled = !sunLight.enabled;
                
                // Adjust moon intensity based on time
                float moonIntensityMultiplier = 1f;
                if (currentTimeOfDay < 0.2f)
                {
                    moonIntensityMultiplier = currentTimeOfDay / 0.2f;
                }
                else if (currentTimeOfDay > 0.8f)
                {
                    moonIntensityMultiplier = (1f - currentTimeOfDay) / 0.2f;
                }
                
                moonLight.intensity = moonIntensity * moonIntensityMultiplier;
            }
        }
        
        private void UpdateAmbientLighting()
        {
            RenderSettings.ambientLight = ambientColor.Evaluate(currentTimeOfDay);
        }
        
        private void UpdateFog()
        {
            if (RenderSettings.fog)
            {
                RenderSettings.fogColor = fogColor.Evaluate(currentTimeOfDay);
                RenderSettings.fogDensity = fogDensity.Evaluate(currentTimeOfDay);
            }
        }
        
        private void UpdateSkybox()
        {
            if (daySkybox != null && nightSkybox != null)
            {
                // Blend between day and night skyboxes
                float dayNightBlend = Mathf.Clamp01((Mathf.Sin((currentTimeOfDay - 0.25f) * Mathf.PI * 2f) + 1f) / 2f);
                
                if (currentSkybox == null)
                {
                    currentSkybox = new Material(daySkybox);
                }
                
                // Simple approach: switch skyboxes based on time
                if (IsDay && RenderSettings.skybox != daySkybox)
                {
                    RenderSettings.skybox = daySkybox;
                }
                else if (IsNight && RenderSettings.skybox != nightSkybox)
                {
                    RenderSettings.skybox = nightSkybox;
                }
            }
        }
        
        public void SetTimeOfDay(float time)
        {
            currentTimeOfDay = Mathf.Clamp01(time);
            UpdateLighting();
        }
        
        public void SetWeather(WeatherType weather, float transitionDuration = 2f)
        {
            if (weatherTransitionCoroutine != null)
            {
                StopCoroutine(weatherTransitionCoroutine);
            }
            
            weatherTransitionCoroutine = StartCoroutine(TransitionWeather(weather, transitionDuration));
        }
        
        private IEnumerator TransitionWeather(WeatherType targetWeather, float duration)
        {
            WeatherType startWeather = currentWeather;
            float elapsedTime = 0f;
            
            while (elapsedTime < duration)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / duration;
                
                // Transition weather effects
                ApplyWeatherTransition(startWeather, targetWeather, progress);
                
                yield return null;
            }
            
            currentWeather = targetWeather;
            ApplyWeather(targetWeather);
        }
        
        private void ApplyWeatherTransition(WeatherType from, WeatherType to, float progress)
        {
            // Fade out old weather effects
            if (from == WeatherType.Rain && rainEffect != null)
            {
                var emission = rainEffect.emission;
                emission.rateOverTime = Mathf.Lerp(emission.rateOverTime.constant, 0f, progress);
            }
            
            if (from == WeatherType.Snow && snowEffect != null)
            {
                var emission = snowEffect.emission;
                emission.rateOverTime = Mathf.Lerp(emission.rateOverTime.constant, 0f, progress);
            }
            
            // Fade in new weather effects
            if (to == WeatherType.Rain && rainEffect != null)
            {
                var emission = rainEffect.emission;
                emission.rateOverTime = Mathf.Lerp(0f, 100f, progress);
            }
            
            if (to == WeatherType.Snow && snowEffect != null)
            {
                var emission = snowEffect.emission;
                emission.rateOverTime = Mathf.Lerp(0f, 50f, progress);
            }
        }
        
        private void ApplyWeather(WeatherType weather)
        {
            // Stop all weather effects first
            if (rainEffect != null) rainEffect.Stop();
            if (snowEffect != null) snowEffect.Stop();
            if (weatherAudioSource != null) weatherAudioSource.Stop();
            
            // Apply specific weather
            switch (weather)
            {
                case WeatherType.Clear:
                    // No additional effects needed
                    break;
                    
                case WeatherType.Rain:
                    if (rainEffect != null)
                    {
                        rainEffect.Play();
                    }
                    if (weatherAudioSource != null && rainSound != null)
                    {
                        weatherAudioSource.clip = rainSound;
                        weatherAudioSource.Play();
                    }
                    break;
                    
                case WeatherType.Snow:
                    if (snowEffect != null)
                    {
                        snowEffect.Play();
                    }
                    break;
                    
                case WeatherType.Windy:
                    if (weatherAudioSource != null && windSound != null)
                    {
                        weatherAudioSource.clip = windSound;
                        weatherAudioSource.Play();
                    }
                    break;
            }
        }
        
        public void PauseTimeProgression(bool pause)
        {
            pauseTimeProgression = pause;
        }
        
        public void SetDayDuration(float duration)
        {
            dayDuration = Mathf.Max(10f, duration);
            timeSpeed = 1f / dayDuration;
        }
        
        private void OnValidate()
        {
            currentTimeOfDay = Mathf.Clamp01(currentTimeOfDay);
            dayDuration = Mathf.Max(10f, dayDuration);
            timeSpeed = 1f / dayDuration;
        }
    }
    
    public enum WeatherType
    {
        Clear,
        Rain,
        Snow,
        Windy,
        Stormy
    }
}
