using UnityEngine;
using UnityEngine.Profiling;
using System.Collections.Generic;
using TMPro;

namespace CrystalQuest.Testing
{
    /// <summary>
    /// Real-time performance monitoring and optimization tools
    /// </summary>
    public class PerformanceMonitor : MonoBehaviour
    {
        [Header("Display Settings")]
        [SerializeField] private bool showPerformanceOverlay = true;
        [SerializeField] private KeyCode toggleKey = KeyCode.F1;
        [SerializeField] private Vector2 overlayPosition = new Vector2(10, 10);
        [SerializeField] private int fontSize = 14;
        
        [Header("Monitoring")]
        [SerializeField] private bool monitorFPS = true;
        [SerializeField] private bool monitorMemory = true;
        [SerializeField] private bool monitorDrawCalls = true;
        [SerializeField] private bool monitorTriangles = true;
        [SerializeField] private float updateInterval = 0.5f;
        
        [Header("Performance Targets")]
        [SerializeField] private int targetFPS = 60;
        [SerializeField] private long maxMemoryMB = 512;
        [SerializeField] private int maxDrawCalls = 1000;
        [SerializeField] private int maxTriangles = 100000;
        
        [Header("Optimization")]
        [SerializeField] private bool enableAutoOptimization = true;
        [SerializeField] private float optimizationThreshold = 0.8f; // 80% of target
        
        // Performance data
        private float currentFPS;
        private float averageFPS;
        private long currentMemory;
        private int currentDrawCalls;
        private int currentTriangles;
        
        // FPS calculation
        private Queue<float> fpsHistory = new Queue<float>();
        private const int FPS_HISTORY_SIZE = 60;
        
        // UI
        private GameObject overlayCanvas;
        private TextMeshProUGUI overlayText;
        private bool overlayVisible = true;
        
        // Timing
        private float lastUpdateTime;
        
        // Optimization state
        private Dictionary<string, bool> optimizationStates = new Dictionary<string, bool>();
        
        private void Awake()
        {
            CreatePerformanceOverlay();
            InitializeOptimizationStates();
        }
        
        private void CreatePerformanceOverlay()
        {
            if (!showPerformanceOverlay) return;
            
            // Create canvas
            overlayCanvas = new GameObject("PerformanceOverlay");
            Canvas canvas = overlayCanvas.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvas.sortingOrder = 1000;
            
            overlayCanvas.AddComponent<UnityEngine.UI.CanvasScaler>();
            overlayCanvas.AddComponent<UnityEngine.UI.GraphicRaycaster>();
            
            // Create text object
            GameObject textObject = new GameObject("PerformanceText");
            textObject.transform.SetParent(overlayCanvas.transform);
            
            overlayText = textObject.AddComponent<TextMeshProUGUI>();
            overlayText.text = "Performance Monitor";
            overlayText.fontSize = fontSize;
            overlayText.color = Color.white;
            overlayText.fontStyle = FontStyles.Bold;
            
            // Position text
            RectTransform rectTransform = textObject.GetComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0, 1);
            rectTransform.anchorMax = new Vector2(0, 1);
            rectTransform.pivot = new Vector2(0, 1);
            rectTransform.anchoredPosition = overlayPosition;
            rectTransform.sizeDelta = new Vector2(300, 200);
            
            DontDestroyOnLoad(overlayCanvas);
        }
        
        private void InitializeOptimizationStates()
        {
            optimizationStates["vsync"] = QualitySettings.vSyncCount > 0;
            optimizationStates["shadows"] = QualitySettings.shadows != ShadowQuality.Disable;
            optimizationStates["antialiasing"] = QualitySettings.antiAliasing > 0;
            optimizationStates["particleLOD"] = true;
            optimizationStates["textureLOD"] = true;
        }
        
        private void Update()
        {
            HandleInput();
            
            if (Time.time - lastUpdateTime >= updateInterval)
            {
                UpdatePerformanceMetrics();
                UpdateOverlay();
                CheckOptimization();
                lastUpdateTime = Time.time;
            }
            
            UpdateFPS();
        }
        
        private void HandleInput()
        {
            if (Input.GetKeyDown(toggleKey))
            {
                ToggleOverlay();
            }
        }
        
        private void UpdateFPS()
        {
            currentFPS = 1f / Time.unscaledDeltaTime;
            
            // Update FPS history
            fpsHistory.Enqueue(currentFPS);
            if (fpsHistory.Count > FPS_HISTORY_SIZE)
            {
                fpsHistory.Dequeue();
            }
            
            // Calculate average FPS
            float total = 0f;
            foreach (float fps in fpsHistory)
            {
                total += fps;
            }
            averageFPS = total / fpsHistory.Count;
        }
        
        private void UpdatePerformanceMetrics()
        {
            if (monitorMemory)
            {
                currentMemory = Profiler.GetTotalAllocatedMemory(false) / (1024 * 1024); // Convert to MB
            }
            
            if (monitorDrawCalls)
            {
                currentDrawCalls = UnityEngine.Rendering.FrameDebugger.enabled ? 
                    UnityEngine.Rendering.FrameDebugger.count : 0;
            }
            
            if (monitorTriangles)
            {
                currentTriangles = UnityStats.triangles;
            }
        }
        
        private void UpdateOverlay()
        {
            if (!overlayVisible || overlayText == null) return;
            
            string overlayContent = "=== PERFORMANCE MONITOR ===\n";
            
            if (monitorFPS)
            {
                Color fpsColor = GetPerformanceColor(currentFPS, targetFPS);
                overlayContent += $"<color=#{ColorUtility.ToHtmlStringRGB(fpsColor)}>FPS: {currentFPS:F1} (Avg: {averageFPS:F1})</color>\n";
            }
            
            if (monitorMemory)
            {
                Color memColor = GetPerformanceColor(maxMemoryMB - currentMemory, maxMemoryMB * 0.5f);
                overlayContent += $"<color=#{ColorUtility.ToHtmlStringRGB(memColor)}>Memory: {currentMemory} MB</color>\n";
            }
            
            if (monitorDrawCalls)
            {
                Color drawColor = GetPerformanceColor(maxDrawCalls - currentDrawCalls, maxDrawCalls * 0.5f);
                overlayContent += $"<color=#{ColorUtility.ToHtmlStringRGB(drawColor)}>Draw Calls: {currentDrawCalls}</color>\n";
            }
            
            if (monitorTriangles)
            {
                Color triColor = GetPerformanceColor(maxTriangles - currentTriangles, maxTriangles * 0.5f);
                overlayContent += $"<color=#{ColorUtility.ToHtmlStringRGB(triColor)}>Triangles: {currentTriangles:N0}</color>\n";
            }
            
            overlayContent += "\n=== SYSTEM INFO ===\n";
            overlayContent += $"Quality: {QualitySettings.names[QualitySettings.GetQualityLevel()]}\n";
            overlayContent += $"Resolution: {Screen.width}x{Screen.height}\n";
            overlayContent += $"VSync: {(QualitySettings.vSyncCount > 0 ? "On" : "Off")}\n";
            
            overlayContent += $"\nPress {toggleKey} to toggle";
            
            overlayText.text = overlayContent;
        }
        
        private Color GetPerformanceColor(float current, float target)
        {
            float ratio = current / target;
            
            if (ratio >= 1f) return Color.green;
            if (ratio >= 0.7f) return Color.yellow;
            return Color.red;
        }
        
        private void CheckOptimization()
        {
            if (!enableAutoOptimization) return;
            
            // Check if performance is below threshold
            bool needsOptimization = false;
            
            if (monitorFPS && currentFPS < targetFPS * optimizationThreshold)
            {
                needsOptimization = true;
            }
            
            if (monitorMemory && currentMemory > maxMemoryMB * optimizationThreshold)
            {
                needsOptimization = true;
            }
            
            if (needsOptimization)
            {
                ApplyOptimizations();
            }
        }
        
        private void ApplyOptimizations()
        {
            Debug.Log("PerformanceMonitor: Applying automatic optimizations");
            
            // Reduce quality settings gradually
            int currentQuality = QualitySettings.GetQualityLevel();
            if (currentQuality > 0)
            {
                QualitySettings.SetQualityLevel(currentQuality - 1);
                Debug.Log($"PerformanceMonitor: Reduced quality level to {QualitySettings.names[currentQuality - 1]}");
            }
            
            // Disable VSync if FPS is low
            if (currentFPS < targetFPS * 0.5f && optimizationStates["vsync"])
            {
                QualitySettings.vSyncCount = 0;
                optimizationStates["vsync"] = false;
                Debug.Log("PerformanceMonitor: Disabled VSync");
            }
            
            // Reduce shadow quality
            if (QualitySettings.shadows != ShadowQuality.Disable && optimizationStates["shadows"])
            {
                QualitySettings.shadows = ShadowQuality.HardOnly;
                optimizationStates["shadows"] = false;
                Debug.Log("PerformanceMonitor: Reduced shadow quality");
            }
            
            // Disable anti-aliasing
            if (QualitySettings.antiAliasing > 0 && optimizationStates["antialiasing"])
            {
                QualitySettings.antiAliasing = 0;
                optimizationStates["antialiasing"] = false;
                Debug.Log("PerformanceMonitor: Disabled anti-aliasing");
            }
        }
        
        public void ToggleOverlay()
        {
            overlayVisible = !overlayVisible;
            if (overlayCanvas != null)
            {
                overlayCanvas.SetActive(overlayVisible);
            }
        }
        
        public void SetTargetFPS(int fps)
        {
            targetFPS = fps;
            Application.targetFrameRate = fps;
        }
        
        public void ForceGarbageCollection()
        {
            System.GC.Collect();
            Debug.Log("PerformanceMonitor: Forced garbage collection");
        }
        
        public PerformanceData GetCurrentPerformanceData()
        {
            return new PerformanceData
            {
                fps = currentFPS,
                averageFPS = averageFPS,
                memoryMB = currentMemory,
                drawCalls = currentDrawCalls,
                triangles = currentTriangles,
                qualityLevel = QualitySettings.GetQualityLevel()
            };
        }
        
        public bool IsPerformanceGood()
        {
            return currentFPS >= targetFPS * optimizationThreshold &&
                   currentMemory <= maxMemoryMB * optimizationThreshold &&
                   currentDrawCalls <= maxDrawCalls * optimizationThreshold;
        }
        
        // Static utility methods
        public static void LogPerformanceWarning(string message)
        {
            Debug.LogWarning($"[PERFORMANCE] {message}");
        }
        
        public static void LogPerformanceInfo(string message)
        {
            Debug.Log($"[PERFORMANCE] {message}");
        }
        
        private void OnDestroy()
        {
            if (overlayCanvas != null)
            {
                Destroy(overlayCanvas);
            }
        }
        
        private void OnValidate()
        {
            targetFPS = Mathf.Max(30, targetFPS);
            maxMemoryMB = Mathf.Max(128, maxMemoryMB);
            maxDrawCalls = Mathf.Max(100, maxDrawCalls);
            maxTriangles = Mathf.Max(10000, maxTriangles);
            updateInterval = Mathf.Max(0.1f, updateInterval);
            optimizationThreshold = Mathf.Clamp01(optimizationThreshold);
        }
    }
    
    [System.Serializable]
    public struct PerformanceData
    {
        public float fps;
        public float averageFPS;
        public long memoryMB;
        public int drawCalls;
        public int triangles;
        public int qualityLevel;
    }
}
