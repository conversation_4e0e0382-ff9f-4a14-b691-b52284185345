using UnityEngine;

namespace CrystalQuest.Core
{
    /// <summary>
    /// Centralized game settings and configuration
    /// </summary>
    [CreateAssetMenu(fileName = "GameSettings", menuName = "Crystal Quest/Game Settings")]
    public class GameSettings : ScriptableObject
    {
        [Header("Game Configuration")]
        [SerializeField] private string gameVersion = "1.0.0";
        [SerializeField] private string gameName = "Crystal Quest 3D";
        [SerializeField] private int maxLevels = 10;
        [SerializeField] private bool enableDebugMode = false;
        
        [Header("Player Settings")]
        [SerializeField] private float defaultWalkSpeed = 5f;
        [SerializeField] private float defaultRunSpeed = 8f;
        [SerializeField] private float defaultJumpHeight = 2f;
        [SerializeField] private float defaultMouseSensitivity = 2f;
        [SerializeField] private int defaultPlayerHealth = 100;
        
        [Header("Gameplay Settings")]
        [SerializeField] private int crystalBaseScore = 100;
        [SerializeField] private int levelCompleteBonus = 500;
        [SerializeField] private float crystalCollectDistance = 2f;
        [SerializeField] private bool enableTimeLimit = false;
        [SerializeField] private float defaultTimeLimit = 300f;
        
        [Header("Audio Settings")]
        [SerializeField] private float defaultMasterVolume = 1f;
        [SerializeField] private float defaultMusicVolume = 0.7f;
        [SerializeField] private float defaultSFXVolume = 0.8f;
        [SerializeField] private float defaultAmbientVolume = 0.5f;
        
        [Header("Graphics Settings")]
        [SerializeField] private int defaultTargetFrameRate = 60;
        [SerializeField] private bool defaultVSyncEnabled = true;
        [SerializeField] private int defaultQualityLevel = 2; // Medium quality
        [SerializeField] private bool defaultFullscreen = true;
        
        [Header("Input Settings")]
        [SerializeField] private KeyCode pauseKey = KeyCode.Escape;
        [SerializeField] private KeyCode interactKey = KeyCode.E;
        [SerializeField] private KeyCode runKey = KeyCode.LeftShift;
        [SerializeField] private KeyCode jumpKey = KeyCode.Space;
        
        // Singleton instance
        private static GameSettings instance;
        public static GameSettings Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = Resources.Load<GameSettings>("GameSettings");
                    if (instance == null)
                    {
                        Debug.LogWarning("GameSettings: No GameSettings asset found in Resources folder. Creating default settings.");
                        instance = CreateInstance<GameSettings>();
                    }
                }
                return instance;
            }
        }
        
        // Properties for easy access
        public string GameVersion => gameVersion;
        public string GameName => gameName;
        public int MaxLevels => maxLevels;
        public bool EnableDebugMode => enableDebugMode;
        
        public float DefaultWalkSpeed => defaultWalkSpeed;
        public float DefaultRunSpeed => defaultRunSpeed;
        public float DefaultJumpHeight => defaultJumpHeight;
        public float DefaultMouseSensitivity => defaultMouseSensitivity;
        public int DefaultPlayerHealth => defaultPlayerHealth;
        
        public int CrystalBaseScore => crystalBaseScore;
        public int LevelCompleteBonus => levelCompleteBonus;
        public float CrystalCollectDistance => crystalCollectDistance;
        public bool EnableTimeLimit => enableTimeLimit;
        public float DefaultTimeLimit => defaultTimeLimit;
        
        public float DefaultMasterVolume => defaultMasterVolume;
        public float DefaultMusicVolume => defaultMusicVolume;
        public float DefaultSFXVolume => defaultSFXVolume;
        public float DefaultAmbientVolume => defaultAmbientVolume;
        
        public int DefaultTargetFrameRate => defaultTargetFrameRate;
        public bool DefaultVSyncEnabled => defaultVSyncEnabled;
        public int DefaultQualityLevel => defaultQualityLevel;
        public bool DefaultFullscreen => defaultFullscreen;
        
        public KeyCode PauseKey => pauseKey;
        public KeyCode InteractKey => interactKey;
        public KeyCode RunKey => runKey;
        public KeyCode JumpKey => jumpKey;
        
        /// <summary>
        /// Apply graphics settings to the application
        /// </summary>
        public void ApplyGraphicsSettings()
        {
            Application.targetFrameRate = defaultTargetFrameRate;
            QualitySettings.vSyncCount = defaultVSyncEnabled ? 1 : 0;
            QualitySettings.SetQualityLevel(defaultQualityLevel);
            
            if (defaultFullscreen)
            {
                Screen.SetResolution(Screen.currentResolution.width, Screen.currentResolution.height, FullScreenMode.FullScreenWindow);
            }
        }
        
        /// <summary>
        /// Load settings from PlayerPrefs
        /// </summary>
        public void LoadSettings()
        {
            // This would load user preferences from PlayerPrefs
            // For now, we'll use the default values
            Debug.Log("GameSettings: Settings loaded");
        }
        
        /// <summary>
        /// Save settings to PlayerPrefs
        /// </summary>
        public void SaveSettings()
        {
            // This would save user preferences to PlayerPrefs
            PlayerPrefs.Save();
            Debug.Log("GameSettings: Settings saved");
        }
        
        /// <summary>
        /// Reset all settings to default values
        /// </summary>
        public void ResetToDefaults()
        {
            PlayerPrefs.DeleteAll();
            Debug.Log("GameSettings: Settings reset to defaults");
        }
        
        private void OnValidate()
        {
            // Ensure values are within reasonable ranges
            defaultWalkSpeed = Mathf.Clamp(defaultWalkSpeed, 1f, 20f);
            defaultRunSpeed = Mathf.Clamp(defaultRunSpeed, defaultWalkSpeed, 30f);
            defaultJumpHeight = Mathf.Clamp(defaultJumpHeight, 0.5f, 10f);
            defaultMouseSensitivity = Mathf.Clamp(defaultMouseSensitivity, 0.1f, 10f);
            defaultPlayerHealth = Mathf.Clamp(defaultPlayerHealth, 1, 1000);
            
            crystalBaseScore = Mathf.Max(crystalBaseScore, 1);
            levelCompleteBonus = Mathf.Max(levelCompleteBonus, 0);
            crystalCollectDistance = Mathf.Clamp(crystalCollectDistance, 0.5f, 10f);
            defaultTimeLimit = Mathf.Clamp(defaultTimeLimit, 30f, 3600f);
            
            defaultMasterVolume = Mathf.Clamp01(defaultMasterVolume);
            defaultMusicVolume = Mathf.Clamp01(defaultMusicVolume);
            defaultSFXVolume = Mathf.Clamp01(defaultSFXVolume);
            defaultAmbientVolume = Mathf.Clamp01(defaultAmbientVolume);
            
            defaultTargetFrameRate = Mathf.Clamp(defaultTargetFrameRate, 30, 240);
            defaultQualityLevel = Mathf.Clamp(defaultQualityLevel, 0, 5);
            maxLevels = Mathf.Clamp(maxLevels, 1, 100);
        }
    }
}
