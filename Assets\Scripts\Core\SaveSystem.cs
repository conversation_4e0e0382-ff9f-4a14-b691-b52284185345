using UnityEngine;
using System.IO;
using System;

namespace CrystalQuest.Core
{
    /// <summary>
    /// Handles game save and load functionality with JSON serialization
    /// </summary>
    public class SaveSystem : MonoBehaviour
    {
        [Header("Save Settings")]
        [SerializeField] private string saveFileName = "gamesave.json";
        [SerializeField] private bool autoSave = true;
        [SerializeField] private float autoSaveInterval = 60f; // seconds
        [SerializeField] private bool encryptSave = false;
        
        // Singleton instance
        public static SaveSystem Instance { get; private set; }
        
        // Events
        public static event Action<GameSaveData> OnGameSaved;
        public static event Action<GameSaveData> OnGameLoaded;
        public static event Action<string> OnSaveError;
        
        // Properties
        public string SaveFilePath => Path.Combine(Application.persistentDataPath, saveFileName);
        public bool HasSaveFile => File.Exists(SaveFilePath);
        
        private float autoSaveTimer;
        private GameSaveData currentSaveData;
        
        private void Awake()
        {
            // Implement singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeSaveSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeSaveSystem()
        {
            // Create save directory if it doesn't exist
            string saveDirectory = Path.GetDirectoryName(SaveFilePath);
            if (!Directory.Exists(saveDirectory))
            {
                Directory.CreateDirectory(saveDirectory);
            }
            
            Debug.Log($"SaveSystem: Initialized. Save path: {SaveFilePath}");
        }
        
        private void Update()
        {
            if (autoSave && GameManager.Instance != null && GameManager.Instance.CurrentState == GameState.Playing)
            {
                autoSaveTimer += Time.deltaTime;
                if (autoSaveTimer >= autoSaveInterval)
                {
                    autoSaveTimer = 0f;
                    SaveGame();
                }
            }
        }
        
        public void SaveGame()
        {
            try
            {
                GameSaveData saveData = CreateSaveData();
                string jsonData = JsonUtility.ToJson(saveData, true);
                
                if (encryptSave)
                {
                    jsonData = EncryptString(jsonData);
                }
                
                File.WriteAllText(SaveFilePath, jsonData);
                currentSaveData = saveData;
                
                OnGameSaved?.Invoke(saveData);
                Debug.Log($"SaveSystem: Game saved successfully at {DateTime.Now}");
            }
            catch (Exception e)
            {
                string errorMessage = $"Failed to save game: {e.Message}";
                Debug.LogError($"SaveSystem: {errorMessage}");
                OnSaveError?.Invoke(errorMessage);
            }
        }
        
        public bool LoadGame()
        {
            try
            {
                if (!HasSaveFile)
                {
                    Debug.LogWarning("SaveSystem: No save file found");
                    return false;
                }
                
                string jsonData = File.ReadAllText(SaveFilePath);
                
                if (encryptSave)
                {
                    jsonData = DecryptString(jsonData);
                }
                
                GameSaveData saveData = JsonUtility.FromJson<GameSaveData>(jsonData);
                ApplySaveData(saveData);
                currentSaveData = saveData;
                
                OnGameLoaded?.Invoke(saveData);
                Debug.Log($"SaveSystem: Game loaded successfully from {File.GetLastWriteTime(SaveFilePath)}");
                return true;
            }
            catch (Exception e)
            {
                string errorMessage = $"Failed to load game: {e.Message}";
                Debug.LogError($"SaveSystem: {errorMessage}");
                OnSaveError?.Invoke(errorMessage);
                return false;
            }
        }
        
        public void DeleteSave()
        {
            try
            {
                if (HasSaveFile)
                {
                    File.Delete(SaveFilePath);
                    currentSaveData = null;
                    Debug.Log("SaveSystem: Save file deleted");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"SaveSystem: Failed to delete save file: {e.Message}");
            }
        }
        
        private GameSaveData CreateSaveData()
        {
            GameSaveData saveData = new GameSaveData();
            
            // Get data from GameManager
            if (GameManager.Instance != null)
            {
                saveData.currentLevel = GameManager.Instance.CurrentLevel;
                saveData.playerScore = GameManager.Instance.PlayerScore;
                saveData.crystalsCollected = GameManager.Instance.CrystalsCollected;
                saveData.totalCrystals = GameManager.Instance.TotalCrystals;
            }
            
            // Get player position
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                saveData.playerPosition = player.transform.position;
                saveData.playerRotation = player.transform.rotation;
            }
            
            // Save settings
            if (AudioManager.Instance != null)
            {
                saveData.masterVolume = AudioManager.Instance.MasterVolume;
                saveData.musicVolume = AudioManager.Instance.MusicVolume;
                saveData.sfxVolume = AudioManager.Instance.SFXVolume;
            }
            
            if (InputManager.Instance != null)
            {
                saveData.mouseSensitivity = InputManager.Instance.MouseSensitivity;
                saveData.invertMouseY = InputManager.Instance.InvertMouseY;
            }
            
            // Save timestamp
            saveData.saveTime = DateTime.Now.ToBinary();
            saveData.gameVersion = Application.version;
            
            return saveData;
        }
        
        private void ApplySaveData(GameSaveData saveData)
        {
            // Apply to GameManager
            if (GameManager.Instance != null)
            {
                // Note: You might want to load the appropriate level here
                // GameManager.Instance.LoadLevel(saveData.currentLevel);
            }
            
            // Apply player position
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                CrystalQuest.Player.PlayerController playerController = player.GetComponent<CrystalQuest.Player.PlayerController>();
                if (playerController != null)
                {
                    playerController.Teleport(saveData.playerPosition);
                    player.transform.rotation = saveData.playerRotation;
                }
            }
            
            // Apply audio settings
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.MasterVolume = saveData.masterVolume;
                AudioManager.Instance.MusicVolume = saveData.musicVolume;
                AudioManager.Instance.SFXVolume = saveData.sfxVolume;
            }
            
            // Apply input settings
            if (InputManager.Instance != null)
            {
                InputManager.Instance.MouseSensitivity = saveData.mouseSensitivity;
                InputManager.Instance.InvertMouseY = saveData.invertMouseY;
            }
        }
        
        private string EncryptString(string text)
        {
            // Simple XOR encryption (not secure, but better than plain text)
            byte[] data = System.Text.Encoding.UTF8.GetBytes(text);
            byte key = 123; // Simple key
            
            for (int i = 0; i < data.Length; i++)
            {
                data[i] = (byte)(data[i] ^ key);
            }
            
            return System.Convert.ToBase64String(data);
        }
        
        private string DecryptString(string encryptedText)
        {
            byte[] data = System.Convert.FromBase64String(encryptedText);
            byte key = 123; // Same key as encryption
            
            for (int i = 0; i < data.Length; i++)
            {
                data[i] = (byte)(data[i] ^ key);
            }
            
            return System.Text.Encoding.UTF8.GetString(data);
        }
        
        public GameSaveData GetCurrentSaveData()
        {
            return currentSaveData;
        }
        
        public DateTime GetSaveTime()
        {
            if (currentSaveData != null)
            {
                return DateTime.FromBinary(currentSaveData.saveTime);
            }
            return DateTime.MinValue;
        }
        
        public void SetAutoSave(bool enabled)
        {
            autoSave = enabled;
            autoSaveTimer = 0f;
        }
        
        public void SetAutoSaveInterval(float interval)
        {
            autoSaveInterval = Mathf.Max(10f, interval); // Minimum 10 seconds
        }
    }
    
    /// <summary>
    /// Data structure for game save information
    /// </summary>
    [System.Serializable]
    public class GameSaveData
    {
        [Header("Game Progress")]
        public int currentLevel = 1;
        public int playerScore = 0;
        public int crystalsCollected = 0;
        public int totalCrystals = 0;
        
        [Header("Player State")]
        public Vector3 playerPosition = Vector3.zero;
        public Quaternion playerRotation = Quaternion.identity;
        public int playerHealth = 100;
        
        [Header("Settings")]
        public float masterVolume = 1f;
        public float musicVolume = 0.7f;
        public float sfxVolume = 0.8f;
        public float mouseSensitivity = 2f;
        public bool invertMouseY = false;
        
        [Header("Meta Data")]
        public long saveTime;
        public string gameVersion = "1.0.0";
        public float playTime = 0f;
        
        public DateTime GetSaveDateTime()
        {
            return DateTime.FromBinary(saveTime);
        }
    }
}
