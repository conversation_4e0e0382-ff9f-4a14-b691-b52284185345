using UnityEngine;
using System.Collections;

namespace CrystalQuest.Environment
{
    /// <summary>
    /// A moving platform that can carry the player and other objects
    /// </summary>
    public class MovingPlatform : MonoBehaviour
    {
        [Header("Movement Settings")]
        [SerializeField] private Transform[] waypoints;
        [SerializeField] private float moveSpeed = 2f;
        [SerializeField] private float waitTime = 1f;
        [SerializeField] private bool loopMovement = true;
        [SerializeField] private bool startMovingImmediately = true;
        
        [Header("Platform Settings")]
        [SerializeField] private bool carryObjects = true;
        [SerializeField] private LayerMask carryLayers = -1;
        
        [Header("Animation")]
        [SerializeField] private AnimationCurve movementCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
        
        // Private variables
        private int currentWaypointIndex = 0;
        private bool isMoving = false;
        private bool movingForward = true;
        private Vector3 lastPosition;
        private Transform platformTransform;
        
        // Objects being carried
        private System.Collections.Generic.List<Transform> carriedObjects = new System.Collections.Generic.List<Transform>();
        
        private void Awake()
        {
            platformTransform = transform;
            lastPosition = platformTransform.position;
        }
        
        private void Start()
        {
            if (waypoints == null || waypoints.Length < 2)
            {
                Debug.LogWarning($"MovingPlatform '{name}': Need at least 2 waypoints to function properly!");
                enabled = false;
                return;
            }
            
            // Start at the first waypoint
            platformTransform.position = waypoints[0].position;
            
            if (startMovingImmediately)
            {
                StartMoving();
            }
        }
        
        private void Update()
        {
            if (isMoving)
            {
                HandleCarriedObjects();
            }
        }
        
        private void HandleCarriedObjects()
        {
            if (!carryObjects) return;
            
            Vector3 deltaMovement = platformTransform.position - lastPosition;
            
            // Move all carried objects with the platform
            for (int i = carriedObjects.Count - 1; i >= 0; i--)
            {
                if (carriedObjects[i] == null)
                {
                    carriedObjects.RemoveAt(i);
                    continue;
                }
                
                carriedObjects[i].position += deltaMovement;
            }
            
            lastPosition = platformTransform.position;
        }
        
        public void StartMoving()
        {
            if (waypoints == null || waypoints.Length < 2) return;
            
            StartCoroutine(MoveBetweenWaypoints());
        }
        
        public void StopMoving()
        {
            StopAllCoroutines();
            isMoving = false;
        }
        
        private IEnumerator MoveBetweenWaypoints()
        {
            isMoving = true;
            
            while (true)
            {
                // Get current and target waypoints
                Vector3 startPosition = platformTransform.position;
                Vector3 targetPosition = waypoints[currentWaypointIndex].position;
                
                // Move to target waypoint
                yield return StartCoroutine(MoveToPosition(startPosition, targetPosition));
                
                // Wait at waypoint
                if (waitTime > 0f)
                {
                    yield return new WaitForSeconds(waitTime);
                }
                
                // Update waypoint index
                UpdateWaypointIndex();
                
                // Check if we should continue looping
                if (!loopMovement && currentWaypointIndex == 0 && !movingForward)
                {
                    break; // Reached the end and not looping
                }
            }
            
            isMoving = false;
        }
        
        private IEnumerator MoveToPosition(Vector3 startPos, Vector3 targetPos)
        {
            float elapsedTime = 0f;
            float totalTime = Vector3.Distance(startPos, targetPos) / moveSpeed;
            
            while (elapsedTime < totalTime)
            {
                elapsedTime += Time.deltaTime;
                float progress = elapsedTime / totalTime;
                
                // Apply animation curve
                float curveValue = movementCurve.Evaluate(progress);
                
                // Update position
                lastPosition = platformTransform.position;
                platformTransform.position = Vector3.Lerp(startPos, targetPos, curveValue);
                
                yield return null;
            }
            
            // Ensure we reach the exact target position
            lastPosition = platformTransform.position;
            platformTransform.position = targetPos;
        }
        
        private void UpdateWaypointIndex()
        {
            if (loopMovement)
            {
                // Simple loop: go to next waypoint, wrap around at the end
                currentWaypointIndex = (currentWaypointIndex + 1) % waypoints.Length;
            }
            else
            {
                // Ping-pong movement: reverse direction at ends
                if (movingForward)
                {
                    currentWaypointIndex++;
                    if (currentWaypointIndex >= waypoints.Length)
                    {
                        currentWaypointIndex = waypoints.Length - 2;
                        movingForward = false;
                    }
                }
                else
                {
                    currentWaypointIndex--;
                    if (currentWaypointIndex < 0)
                    {
                        currentWaypointIndex = 1;
                        movingForward = true;
                    }
                }
            }
        }
        
        private void OnTriggerEnter(Collider other)
        {
            if (!carryObjects) return;
            
            // Check if the object should be carried
            if (((1 << other.gameObject.layer) & carryLayers) != 0)
            {
                if (!carriedObjects.Contains(other.transform))
                {
                    carriedObjects.Add(other.transform);
                    Debug.Log($"MovingPlatform: Started carrying {other.name}");
                }
            }
        }
        
        private void OnTriggerExit(Collider other)
        {
            if (!carryObjects) return;
            
            // Remove object from carried list
            if (carriedObjects.Contains(other.transform))
            {
                carriedObjects.Remove(other.transform);
                Debug.Log($"MovingPlatform: Stopped carrying {other.name}");
            }
        }
        
        private void OnDrawGizmosSelected()
        {
            if (waypoints == null || waypoints.Length < 2) return;
            
            // Draw waypoints and path
            Gizmos.color = Color.blue;
            
            for (int i = 0; i < waypoints.Length; i++)
            {
                if (waypoints[i] == null) continue;
                
                // Draw waypoint
                Gizmos.DrawWireSphere(waypoints[i].position, 0.5f);
                
                // Draw path to next waypoint
                int nextIndex = loopMovement ? (i + 1) % waypoints.Length : i + 1;
                if (nextIndex < waypoints.Length && waypoints[nextIndex] != null)
                {
                    Gizmos.DrawLine(waypoints[i].position, waypoints[nextIndex].position);
                }
            }
            
            // Draw current position if playing
            if (Application.isPlaying)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireCube(transform.position, Vector3.one);
            }
        }
        
        // Public methods for external control
        public void SetMoveSpeed(float newSpeed)
        {
            moveSpeed = Mathf.Max(0.1f, newSpeed);
        }
        
        public void SetWaitTime(float newWaitTime)
        {
            waitTime = Mathf.Max(0f, newWaitTime);
        }
        
        public bool IsMoving => isMoving;
        public int CurrentWaypointIndex => currentWaypointIndex;
        public int CarriedObjectCount => carriedObjects.Count;
    }
}
