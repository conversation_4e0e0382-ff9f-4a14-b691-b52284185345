using UnityEngine;
using CrystalQuest.Core;

namespace CrystalQuest.Player
{
    /// <summary>
    /// Handles player movement, input, and basic interactions
    /// </summary>
    [RequireComponent(typeof(CharacterController))]
    [RequireComponent(typeof(Animator))]
    public class PlayerController : MonoBehaviour
    {
        [Header("Movement Settings")]
        [SerializeField] private float walkSpeed = 5f;
        [SerializeField] private float runSpeed = 8f;
        [SerializeField] private float jumpHeight = 2f;
        [SerializeField] private float gravity = -9.81f;
        [SerializeField] private float groundCheckDistance = 0.1f;
        
        [Header("Camera Settings")]
        [SerializeField] private Transform cameraTransform;
        [SerializeField] private float mouseSensitivity = 2f;
        [SerializeField] private float maxLookAngle = 80f;
        
        [Header("Ground Check")]
        [SerializeField] private Transform groundCheck;
        [SerializeField] private LayerMask groundMask = 1;
        
        [Head<PERSON>("Audio")]
        [SerializeField] private float footstepInterval = 0.5f;
        
        // Components
        private CharacterController characterController;
        private Animator animator;
        
        // Movement variables
        private Vector3 velocity;
        private bool isGrounded;
        private bool isRunning;
        private float currentSpeed;
        
        // Camera variables
        private float mouseX;
        private float mouseY;
        private float xRotation = 0f;
        
        // Audio variables
        private float footstepTimer;
        
        // Input variables
        private Vector2 movementInput;
        private bool jumpInput;
        private bool runInput;
        
        // Animation hashes for performance
        private static readonly int SpeedHash = Animator.StringToHash("Speed");
        private static readonly int IsGroundedHash = Animator.StringToHash("IsGrounded");
        private static readonly int JumpHash = Animator.StringToHash("Jump");
        private static readonly int IsRunningHash = Animator.StringToHash("IsRunning");
        
        private void Awake()
        {
            characterController = GetComponent<CharacterController>();
            animator = GetComponent<Animator>();
            
            // Set up camera if not assigned
            if (cameraTransform == null)
            {
                Camera mainCamera = Camera.main;
                if (mainCamera != null)
                {
                    cameraTransform = mainCamera.transform;
                }
            }
            
            // Set up ground check if not assigned
            if (groundCheck == null)
            {
                GameObject groundCheckGO = new GameObject("GroundCheck");
                groundCheckGO.transform.SetParent(transform);
                groundCheckGO.transform.localPosition = new Vector3(0, -1f, 0);
                groundCheck = groundCheckGO.transform;
            }
        }
        
        private void Start()
        {
            // Lock cursor when game starts
            if (GameManager.Instance != null && GameManager.Instance.CurrentState == GameState.Playing)
            {
                Cursor.lockState = CursorLockMode.Locked;
                Cursor.visible = false;
            }
        }
        
        private void Update()
        {
            // Only handle input if game is playing
            if (GameManager.Instance != null && GameManager.Instance.CurrentState != GameState.Playing)
                return;
                
            HandleInput();
            HandleMouseLook();
            HandleMovement();
            HandleAnimation();
            HandleAudio();
        }
        
        private void HandleInput()
        {
            // Movement input
            movementInput.x = Input.GetAxis("Horizontal");
            movementInput.y = Input.GetAxis("Vertical");
            
            // Jump input
            jumpInput = Input.GetButtonDown("Jump");
            
            // Run input
            runInput = Input.GetKey(KeyCode.LeftShift);
            
            // Interaction input
            if (Input.GetKeyDown(KeyCode.E))
            {
                TryInteract();
            }
        }
        
        private void HandleMouseLook()
        {
            if (cameraTransform == null) return;
            
            // Get mouse input
            mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
            mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;
            
            // Rotate the player body horizontally
            transform.Rotate(Vector3.up * mouseX);
            
            // Rotate the camera vertically
            xRotation -= mouseY;
            xRotation = Mathf.Clamp(xRotation, -maxLookAngle, maxLookAngle);
            cameraTransform.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
        }
        
        private void HandleMovement()
        {
            // Ground check
            isGrounded = Physics.CheckSphere(groundCheck.position, groundCheckDistance, groundMask);
            
            if (isGrounded && velocity.y < 0)
            {
                velocity.y = -2f; // Small negative value to keep grounded
            }
            
            // Calculate movement direction
            Vector3 moveDirection = transform.right * movementInput.x + transform.forward * movementInput.y;
            moveDirection.Normalize();
            
            // Determine speed
            isRunning = runInput && movementInput.magnitude > 0.1f;
            currentSpeed = isRunning ? runSpeed : walkSpeed;
            
            // Apply movement
            characterController.Move(moveDirection * currentSpeed * Time.deltaTime);
            
            // Handle jumping
            if (jumpInput && isGrounded)
            {
                velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);
                
                // Play jump sound
                if (AudioManager.Instance != null)
                {
                    AudioManager.Instance.PlayJumpSound();
                }
            }
            
            // Apply gravity
            velocity.y += gravity * Time.deltaTime;
            characterController.Move(velocity * Time.deltaTime);
        }
        
        private void HandleAnimation()
        {
            if (animator == null) return;
            
            // Calculate speed for animation
            float animationSpeed = movementInput.magnitude * (isRunning ? 2f : 1f);
            
            // Update animator parameters
            animator.SetFloat(SpeedHash, animationSpeed);
            animator.SetBool(IsGroundedHash, isGrounded);
            animator.SetBool(IsRunningHash, isRunning);
            
            // Trigger jump animation
            if (jumpInput && isGrounded)
            {
                animator.SetTrigger(JumpHash);
            }
        }
        
        private void HandleAudio()
        {
            // Play footstep sounds when moving on ground
            if (isGrounded && movementInput.magnitude > 0.1f)
            {
                footstepTimer += Time.deltaTime;
                
                float currentFootstepInterval = isRunning ? footstepInterval * 0.7f : footstepInterval;
                
                if (footstepTimer >= currentFootstepInterval)
                {
                    footstepTimer = 0f;
                    
                    if (AudioManager.Instance != null)
                    {
                        AudioManager.Instance.PlayFootstepSound();
                    }
                }
            }
            else
            {
                footstepTimer = 0f;
            }
        }
        
        private void TryInteract()
        {
            // Raycast to check for interactable objects
            Ray ray = new Ray(cameraTransform.position, cameraTransform.forward);
            RaycastHit hit;
            
            if (Physics.Raycast(ray, out hit, 3f))
            {
                // Check for crystal
                Crystal crystal = hit.collider.GetComponent<Crystal>();
                if (crystal != null)
                {
                    crystal.Collect();
                    return;
                }
                
                // Check for other interactables
                IInteractable interactable = hit.collider.GetComponent<IInteractable>();
                if (interactable != null)
                {
                    interactable.Interact();
                }
            }
        }
        
        public void SetMovementEnabled(bool enabled)
        {
            this.enabled = enabled;
        }
        
        public void Teleport(Vector3 position)
        {
            characterController.enabled = false;
            transform.position = position;
            characterController.enabled = true;
            velocity = Vector3.zero;
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw ground check sphere
            if (groundCheck != null)
            {
                Gizmos.color = isGrounded ? Color.green : Color.red;
                Gizmos.DrawWireSphere(groundCheck.position, groundCheckDistance);
            }
            
            // Draw interaction ray
            if (cameraTransform != null)
            {
                Gizmos.color = Color.blue;
                Gizmos.DrawRay(cameraTransform.position, cameraTransform.forward * 3f);
            }
        }
    }
    
    /// <summary>
    /// Interface for objects that can be interacted with
    /// </summary>
    public interface IInteractable
    {
        void Interact();
    }
}
